<?php
/**
 * ملف اختبار شامل للنظام المحدث
 * يختبر جميع المكونات والملفات والروابط
 */

// تحميل النظام
require_once 'config/autoload.php';

// بدء الاختبار
echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار النظام - WIDDX OMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='assets/css/main.css' rel='stylesheet'>
    <link href='assets/css/components.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <div class='card widdx-card'>
        <div class='widdx-card-header text-center'>
            <h2><i class='fas fa-check-circle'></i> اختبار النظام الشامل</h2>
        </div>
        <div class='widdx-card-body'>";

$allTestsPassed = true;
$testResults = [];

// 1. اختبار قاعدة البيانات
echo "<h3><i class='fas fa-database'></i> 1. اختبار قاعدة البيانات</h3>";
try {
    $db = getDatabase();
    if ($db->isConnected()) {
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات: نجح</div>";
        $testResults['database'] = true;
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> الاتصال بقاعدة البيانات: فشل</div>";
        $testResults['database'] = false;
        $allTestsPassed = false;
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    $testResults['database'] = false;
    $allTestsPassed = false;
}

// 2. اختبار الملفات الأساسية
echo "<h3><i class='fas fa-file-code'></i> 2. اختبار الملفات الأساسية</h3>";
$requiredFiles = [
    'config/config.php' => 'ملف الإعدادات',
    'config/database_unified.php' => 'قاعدة البيانات الموحدة',
    'config/autoload.php' => 'التحميل التلقائي',
    'includes/header.php' => 'Header مشترك',
    'includes/footer.php' => 'Footer مشترك',
    'includes/layout.php' => 'Layout موحد',
    'assets/css/main.css' => 'CSS الرئيسي',
    'assets/css/components.css' => 'CSS المكونات',
    'assets/js/main.js' => 'JavaScript الرئيسي',
    'assets/js/widdx-core.js' => 'JavaScript الأساسي',
    'classes/Database.php' => 'كلاس قاعدة البيانات',
    'classes/Customer.php' => 'كلاس العملاء',
    'classes/Order.php' => 'كلاس الطلبيات'
];

$filesTestPassed = true;
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> {$description}: موجود (" . number_format($size) . " بايت)</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> {$description}: غير موجود</div>";
        $filesTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['files'] = $filesTestPassed;

// 3. اختبار الصفحات الرئيسية
echo "<h3><i class='fas fa-globe'></i> 3. اختبار الصفحات الرئيسية</h3>";
$pages = [
    'index.php' => 'الصفحة الرئيسية',
    'add_customer.php' => 'إضافة عميل',
    'add_order.php' => 'إضافة طلبية',
    'view_orders.php' => 'عرض الطلبيات',
    'manage_customers.php' => 'إدارة العملاء',
    'view_order.php' => 'عرض تفاصيل الطلبية'
];

$pagesTestPassed = true;
foreach ($pages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> {$description}: موجود</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> {$description}: غير موجود</div>";
        $pagesTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['pages'] = $pagesTestPassed;

// 4. اختبار المجلدات المطلوبة
echo "<h3><i class='fas fa-folder'></i> 4. اختبار المجلدات المطلوبة</h3>";
$directories = [
    'uploads' => 'مجلد الصور',
    'logs' => 'مجلد السجلات',
    'assets/images' => 'مجلد الصور الثابتة'
];

$dirsTestPassed = true;
foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        $alertClass = is_writable($dir) ? 'alert-success' : 'alert-warning';
        echo "<div class='alert {$alertClass}'><i class='fas fa-check'></i> {$description}: موجود ({$writable})</div>";
        if (!is_writable($dir)) {
            $dirsTestPassed = false;
        }
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> {$description}: غير موجود</div>";
        $dirsTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['directories'] = $dirsTestPassed;

// 5. اختبار الكلاسات
echo "<h3><i class='fas fa-code'></i> 5. اختبار الكلاسات</h3>";
$classes = ['WIDDXDatabase', 'Customer', 'Order'];
$classesTestPassed = true;

foreach ($classes as $className) {
    if (class_exists($className)) {
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> كلاس {$className}: محمل بنجاح</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> كلاس {$className}: غير محمل</div>";
        $classesTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['classes'] = $classesTestPassed;

// 6. اختبار JavaScript و CSS
echo "<h3><i class='fas fa-palette'></i> 6. اختبار الموارد الخارجية</h3>";
echo "<div class='alert alert-info'><i class='fas fa-info'></i> تحقق من تحميل Bootstrap و Font Awesome في المتصفح</div>";

// 7. النتيجة النهائية
echo "<hr><h3><i class='fas fa-chart-pie'></i> النتيجة النهائية</h3>";

if ($allTestsPassed) {
    echo "<div class='alert alert-success widdx-alert-success'>
        <h4><i class='fas fa-check-circle'></i> جميع الاختبارات نجحت!</h4>
        <p>النظام جاهز للاستخدام بشكل كامل.</p>
    </div>";
} else {
    echo "<div class='alert alert-warning widdx-alert-warning'>
        <h4><i class='fas fa-exclamation-triangle'></i> بعض الاختبارات فشلت</h4>
        <p>يرجى مراجعة الأخطاء أعلاه وإصلاحها.</p>
    </div>";
}

// إحصائيات الاختبار
$passedTests = array_sum($testResults);
$totalTests = count($testResults);
$successRate = round(($passedTests / $totalTests) * 100, 2);

echo "<div class='row mt-4'>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h5 class='text-success'>{$passedTests}</h5>
                <small>اختبارات نجحت</small>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h5 class='text-danger'>" . ($totalTests - $passedTests) . "</h5>
                <small>اختبارات فشلت</small>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h5 class='text-info'>{$totalTests}</h5>
                <small>إجمالي الاختبارات</small>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h5 class='text-primary'>{$successRate}%</h5>
                <small>معدل النجاح</small>
            </div>
        </div>
    </div>
</div>";

echo "<div class='mt-4 text-center'>
    <a href='index.php' class='btn widdx-btn widdx-btn-primary'>
        <i class='fas fa-home'></i> الانتقال للصفحة الرئيسية
    </a>
    <button onclick='window.location.reload()' class='btn widdx-btn btn-outline-secondary'>
        <i class='fas fa-redo'></i> إعادة الاختبار
    </button>
</div>";

echo "        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/js/widdx-core.js'></script>
<script src='assets/js/main.js'></script>
<script>
    // اختبار JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ JavaScript تم تحميله بنجاح');
        
        // اختبار WIDDXCore
        if (typeof WIDDXCore !== 'undefined') {
            console.log('✅ WIDDXCore تم تحميله بنجاح');
        } else {
            console.error('❌ WIDDXCore لم يتم تحميله');
        }
        
        // اختبار widdxSystem
        if (typeof widdxSystem !== 'undefined') {
            console.log('✅ widdxSystem تم تحميله بنجاح');
        } else {
            console.log('⚠️ widdxSystem غير متوفر (هذا طبيعي)');
        }
    });
</script>
</body>
</html>";
?>
