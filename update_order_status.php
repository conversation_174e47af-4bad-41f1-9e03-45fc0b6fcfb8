<?php
/**
 * ملف تحديث حالة الطلبية المحسن
 * يدعم AJAX والتحديث المباشر مع معالجة أخطاء متقدمة
 */

// إعداد الرؤوس
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

// تحميل النظام
require_once 'config/database_fixed.php';

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed', 405);
    }

    // التحقق من وجود البيانات المطلوبة
    if (!isset($_POST['order_id']) || !isset($_POST['status']) || !isset($_POST['update_status'])) {
        throw new Exception('بيانات غير مكتملة', 400);
    }

    $orderId = (int)$_POST['order_id'];
    $newStatus = trim($_POST['status']);

    // التحقق من صحة البيانات
    if ($orderId <= 0) {
        throw new Exception('معرف الطلبية غير صحيح', 400);
    }

    // الحالات المسموحة
    $allowedStatuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($newStatus, $allowedStatuses)) {
        throw new Exception('حالة غير صحيحة', 400);
    }

    // إنشاء اتصال قاعدة البيانات
    $db = getDatabase();

    // التحقق من وجود الطلبية وجلب بياناتها
    $checkQuery = "SELECT o.id, o.status, o.customer_id, o.total_amount, c.name as customer_name
                   FROM orders o
                   JOIN customers c ON o.customer_id = c.id
                   WHERE o.id = :order_id";
    $db->query($checkQuery);
    $db->bind(':order_id', $orderId);
    $order = $db->single();

    if (!$order) {
        throw new Exception('الطلبية غير موجودة', 404);
    }

    // التحقق من إمكانية التحديث
    $currentStatus = $order['status'];
    if ($currentStatus === $newStatus) {
        throw new Exception('الطلبية لها نفس الحالة المطلوبة', 400);
    }

    // منع تحديث الطلبيات المكتملة أو الملغية (إلا في حالات خاصة)
    if (in_array($currentStatus, ['completed', 'cancelled']) && !isset($_POST['force_update'])) {
        throw new Exception('لا يمكن تحديث طلبية مكتملة أو ملغية', 403);
    }

    // بدء المعاملة
    $db->beginTransaction();

    try {
        // تحديث حالة الطلبية
        $updateQuery = "UPDATE orders SET
                        status = :status,
                        updated_at = NOW()
                        WHERE id = :order_id";

        $db->query($updateQuery);
        $db->bind(':status', $newStatus);
        $db->bind(':order_id', $orderId);

        if (!$db->execute()) {
            throw new Exception('فشل في تحديث الطلبية');
        }

        // تحديث إحصائيات العميل إذا لزم الأمر
        if ($newStatus === 'completed') {
            $updateCustomerQuery = "UPDATE customers SET
                                   total_orders = (SELECT COUNT(*) FROM orders WHERE customer_id = :customer_id),
                                   total_spent = (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE customer_id = :customer_id AND status = 'completed')
                                   WHERE id = :customer_id";

            $db->query($updateCustomerQuery);
            $db->bind(':customer_id', $order['customer_id']);
            $db->execute();
        }

        // تأكيد المعاملة
        $db->commit();

        // إعداد الاستجابة الناجحة
        $statusTexts = [
            'pending' => 'معلقة',
            'processing' => 'قيد التنفيذ',
            'completed' => 'مكتملة',
            'cancelled' => 'ملغية'
        ];

        $response = [
            'success' => true,
            'message' => "تم تحديث طلبية #{$orderId} للعميل {$order['customer_name']} إلى: {$statusTexts[$newStatus]}",
            'data' => [
                'order_id' => $orderId,
                'customer_name' => $order['customer_name'],
                'old_status' => $currentStatus,
                'new_status' => $newStatus,
                'status_text' => $statusTexts[$newStatus],
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'timestamp' => time()
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        // التراجع عن المعاملة
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode() ?: 500;
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => $errorCode,
        'timestamp' => time()
    ];

    // تحديد رمز الاستجابة HTTP
    http_response_code($errorCode);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

    // تسجيل الخطأ
    error_log("Order status update error: " . $e->getMessage() . " (Order ID: " . ($orderId ?? 'unknown') . ")");

} finally {
    // تنظيف الموارد
    if (isset($db)) {
        $db = null;
    }
}
?>
