<?php
/**
 * إصلاح سريع لمشكلة إنشاء الطلبيات
 */

require_once 'config/database.php';

echo "<h2>🚀 إصلاح سريع لمشكلة إنشاء الطلبيات</h2>";

try {
    // إنشاء اتصال مباشر
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح";
    echo "</div>";
    
    // 1. التحقق من وجود العمود product_image
    echo "<h3>🔍 فحص العمود product_image</h3>";
    
    $result = $pdo->query("SHOW COLUMNS FROM order_items LIKE 'product_image'");
    
    if ($result->rowCount() == 0) {
        echo "<div style='background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ العمود product_image غير موجود - جاري الإصلاح...";
        echo "</div>";
        
        // إضافة العمود
        $pdo->exec("ALTER TABLE order_items ADD COLUMN product_image VARCHAR(255) AFTER product_name");
        
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ تم إضافة العمود product_image بنجاح!";
        echo "</div>";
    } else {
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ العمود product_image موجود";
        echo "</div>";
    }
    
    // 2. إنشاء مجلد uploads
    echo "<h3>📁 فحص مجلد uploads</h3>";
    
    $upload_dir = __DIR__ . '/uploads';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ تم إنشاء مجلد uploads";
            echo "</div>";
        } else {
            echo "<div style='background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ فشل في إنشاء مجلد uploads";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ مجلد uploads موجود";
        echo "</div>";
    }
    
    // 3. اختبار إنشاء طلبية
    echo "<h3>🧪 اختبار إنشاء طلبية</h3>";
    
    // التحقق من وجود عملاء
    $customerResult = $pdo->query("SELECT COUNT(*) as count FROM customers");
    $customerCount = $customerResult->fetch()['count'];
    
    if ($customerCount > 0) {
        // جلب أول عميل
        $customerResult = $pdo->query("SELECT id, name FROM customers LIMIT 1");
        $customer = $customerResult->fetch();
        
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "ℹ️ سيتم اختبار إنشاء طلبية للعميل: " . htmlspecialchars($customer['name']);
        echo "</div>";
        
        // محاولة إنشاء طلبية تجريبية
        $pdo->beginTransaction();
        
        try {
            // إنشاء الطلبية
            $stmt = $pdo->prepare("INSERT INTO orders (customer_id, notes, status, total_amount) VALUES (?, ?, ?, ?)");
            $stmt->execute([$customer['id'], 'طلبية تجريبية للاختبار', 'pending', 150.00]);
            $orderId = $pdo->lastInsertId();
            
            // إضافة عنصر للطلبية
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $orderId,
                'منتج تجريبي',
                null,
                2,
                120.00,
                180.00,
                40.00,
                75.00,
                150.00,
                'عنصر تجريبي للاختبار'
            ]);
            
            // حذف الطلبية التجريبية
            $pdo->exec("DELETE FROM order_items WHERE order_id = $orderId");
            $pdo->exec("DELETE FROM orders WHERE id = $orderId");
            
            $pdo->commit();
            
            echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border: 2px solid #28a745;'>";
            echo "<strong>🎉 نجح الاختبار! النظام يعمل بشكل صحيح الآن.</strong>";
            echo "</div>";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0; border: 2px solid #dc3545;'>";
            echo "<strong>❌ فشل الاختبار:</strong><br>" . $e->getMessage();
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ لا توجد عملاء في قاعدة البيانات";
        echo "<br>يجب إضافة عميل أولاً من <a href='add_customer.php'>صفحة إضافة عميل</a>";
        echo "</div>";
    }
    
    // 4. مسح سجل الأخطاء
    echo "<h3>🧹 تنظيف سجل الأخطاء</h3>";
    
    $log_file = __DIR__ . '/logs/database_errors.log';
    if (file_exists($log_file)) {
        file_put_contents($log_file, '');
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ تم مسح سجل الأخطاء";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 تم الانتهاء من الإصلاح!</h3>";
    echo "<p>يمكنك الآن تجربة إنشاء طلبية جديدة.</p>";
    echo "<a href='add_order.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة طلبية جديدة</a>";
    echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>العودة للرئيسية</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0; border: 2px solid #dc3545;'>";
    echo "<h4>❌ خطأ في قاعدة البيانات:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<h5>تأكد من:</h5>";
    echo "<ul>";
    echo "<li>أن خادم MySQL يعمل</li>";
    echo "<li>أن إعدادات قاعدة البيانات صحيحة</li>";
    echo "<li>أن قاعدة البيانات widdx_oms موجودة</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ خطأ عام:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح سريع - WIDDX OMS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى تم عرضه أعلاه -->
    </div>
</body>
</html>
