[2025-06-11 10:14:56] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:14:56] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:01] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:01] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:10] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:10] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:11] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:11] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:13] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:13] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:14] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:15:14] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:16:07] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:16:08] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 10:16:17] Query preparation failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 | Query: SHOW TABLES LIKE :tableName
[2025-06-11 11:41:09] Query preparation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'product_image' in 'field list' | Query: INSERT INTO order_items (order_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes) 
                                   VALUES (:order_id, :product_name, :product_image, :quantity, :width, :height, :depth, :unit_price, :total_price, :notes)
[2025-06-11 15:50:03] Query preparation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'product_image' in 'field list' | Query: INSERT INTO order_items (order_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes) 
                                   VALUES (:order_id, :product_name, :product_image, :quantity, :width, :height, :depth, :unit_price, :total_price, :notes)
[2025-06-21 11:46:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:47:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:48:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:49:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:50:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:51:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:52:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:53:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:54:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:55:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:56:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:57:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:58:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 11:59:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:00:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:01:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:02:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:03:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:04:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:05:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:06:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:07:36] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:08:20] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:08:20] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 12:08:21] Query execution failed: SQLSTATE[HY093]: Invalid parameter number | Query: 
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today THEN 1 ELSE 0 END) as today_orders,

            -- عملاء جدد اليوم
            (SELECT COUNT(*) FROM customers WHERE DATE(created_at) = :today) as today_customers,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    
[2025-06-21 13:54:47] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:54:49] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:55:49] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:56:49] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:57:22] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:57:25] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:57:30] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:58:31] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 13:59:31] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
[2025-06-21 14:00:31] ERROR: Query execution failed: SQLSTATE[HY093]: Invalid parameter number
