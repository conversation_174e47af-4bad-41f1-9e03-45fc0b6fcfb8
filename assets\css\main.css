/* ملف CSS الرئيسي المحسن لنظام WIDDX OMS */

/* متغيرات CSS للألوان والقيم */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* الإعدادات العامة المحسنة */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-family);
    background-color: var(--light-color);
    direction: rtl;
    line-height: 1.6;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* الوضع الليلي */
body.dark-mode {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

body.dark-mode .card {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-mode .navbar-dark {
    background-color: #000 !important;
}

/* تحسينات شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: bold;
    color: #2c3e50 !important;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    font-weight: 600;
}

/* البطاقات المحسنة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0,0,0,0.03);
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* الأزرار المحسنة */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #ffb300 100%);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.btn-sm {
    padding: 6px 16px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.125rem;
}

/* أزرار عائمة */
.floating-buttons .btn {
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.floating-buttons .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
    color: #212529;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: white;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white;
}

/* أيقونات الميزات */
.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

/* أزرار العملاء */
.customer-button {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px !important;
    transition: all 0.3s ease;
}

.customer-button:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.customer-button:not(.collapsed) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* إحصائيات العملاء */
.customer-stats .badge {
    font-size: 0.75rem;
}

/* الأكورديون */
.accordion-item {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.accordion-body {
    background-color: #f8f9fa;
}

/* صفوف الطلبيات */
.order-row {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.order-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* عناصر الطلبيات */
.order-item {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* شارات الحالة */
.status-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* صفوف العملاء */
.customer-row {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.customer-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.customer-active {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
}

.customer-inactive {
    border-left: 4px solid #6c757d;
    opacity: 0.8;
}

/* نماذج الإدخال المحسنة */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: var(--transition);
    background-color: #fff;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-control:hover, .form-select:hover {
    border-color: #ced4da;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* تحسينات للنماذج في الوضع الليلي */
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #2d2d2d;
    border-color: #495057;
    color: #e0e0e0;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background-color: #2d2d2d;
    border-color: var(--primary-color);
}

/* مجموعات النماذج */
.input-group {
    border-radius: 10px;
    overflow: hidden;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-radius: 10px 0 0 10px;
}

.input-group .form-control:last-child {
    border-radius: 0 10px 10px 0;
}

.input-group-text {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 500;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
}

/* تحسينات الشاشات الصغيرة */
@media (max-width: 768px) {
    .customer-stats {
        margin-top: 10px;
    }
    
    .customer-stats .badge {
        display: block;
        margin-bottom: 5px;
    }
    
    .order-row .row > div {
        margin-bottom: 10px;
    }
}

/* تحسينات إضافية */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات الألوان */
.text-primary-custom {
    color: #667eea !important;
}

.bg-primary-custom {
    background-color: #667eea !important;
}

.border-primary-custom {
    border-color: #667eea !important;
}

/* تحسينات التفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    opacity: 0.8;
}

/* تحسينات النصوص */
.text-truncate-custom {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* تحسينات الجداول المتقدمة */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    border-color: #e9ecef;
    vertical-align: middle;
    padding: 1rem;
    transition: var(--transition);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.02);
}

/* جداول البيانات التفاعلية */
.data-table {
    position: relative;
}

.data-table .table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

/* تحسينات الرسائل والتنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border-left: 4px solid;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left-color: var(--info-color);
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: var(--warning-color);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: var(--danger-color);
}

.alert i {
    margin-left: 8px;
    font-size: 1.1em;
}

/* تأثيرات الرسائل */
.alert.fade.show {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسينات الأيقونات المتقدمة */
.icon-lg {
    font-size: 1.5rem;
}

.icon-xl {
    font-size: 2rem;
}

.icon-xxl {
    font-size: 3rem;
}

.icon-animated {
    transition: var(--transition);
}

.icon-animated:hover {
    transform: rotate(360deg) scale(1.2);
}

.icon-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تحسينات الشارات */
.badge {
    border-radius: 20px;
    padding: 0.5em 0.8em;
    font-weight: 500;
    font-size: 0.75em;
}

.badge-animated {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* تحسينات الفوتر */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

footer .container {
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

/* تحسينات الأكورديون */
.accordion-item {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.accordion-button {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.accordion-body {
    background-color: #f8f9fa;
    padding: 1.5rem;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    backdrop-filter: blur(10px);
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    transform: translateX(5px);
}

.dropdown-header {
    font-weight: 600;
    color: var(--primary-color);
    padding: 0.75rem 1.5rem 0.5rem;
}

/* تحسينات النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    overflow: hidden;
}

.modal-header {
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
}

/* تأثيرات التحميل المحسنة */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* تحسينات الشاشات الصغيرة المتقدمة */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .floating-buttons .btn {
        width: 45px;
        height: 45px;
    }

    .navbar-brand span {
        display: none;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }

    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }

    .alert {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}
