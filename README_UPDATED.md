# 🚀 WIDDX OMS - نظام إدارة الطلبيات المحسن

نظام شامل ومحسن لإدارة الطلبيات والعملاء مع واجهة مستخدم متطورة وميزات متقدمة.

## ✨ التحديثات الجديدة (الإصدار 2.1.0)

### 🎯 تحسينات الأداء والبنية
- **نظام Layout موحد**: استخدام Layout مشترك لجميع الصفحات مع `includes/layout.php`
- **قاعدة بيانات موحدة**: نظام قاعدة بيانات محسن مع Singleton Pattern في `config/database_unified.php`
- **تحميل محسن**: تحسين تحميل CSS و JavaScript مع ملفات منفصلة
- **استجابة سريعة**: تحسينات في سرعة الاستجابة والتفاعل
- **إزالة التكرار**: حذف الملفات المكررة وغير المستخدمة

### 📁 هيكل المجلدات المحدث
```
├── assets/
│   ├── css/
│   │   ├── main.css (محسن)
│   │   └── components.css (جديد)
│   ├── js/
│   │   ├── main.js (محسن)
│   │   └── widdx-core.js (جديد)
│   └── images/
├── classes/
│   ├── Database.php
│   ├── Customer.php
│   └── Order.php
├── config/
│   ├── config.php (محسن)
│   ├── database_unified.php (جديد)
│   └── autoload.php (محسن)
├── includes/
│   ├── header.php (محسن)
│   ├── footer.php (محسن)
│   └── layout.php (جديد)
├── api/
│   └── quick_stats.php (محسن)
├── logs/
└── uploads/
```

### 🎨 تحسينات الواجهة
- **تصميم موحد**: استخدام header و footer مشتركين مع تحسينات بصرية
- **مكونات CSS محسنة**: ملف `components.css` للمكونات المخصصة
- **تأثيرات بصرية**: رسوم متحركة وتأثيرات تفاعلية محسنة
- **استجابة محسنة**: تحسينات للشاشات المختلفة
- **إشعارات ذكية**: نظام إشعارات في الوقت الفعلي

### 🔧 تحسينات تقنية
- **JavaScript محسن**: ملف `widdx-core.js` للوظائف الأساسية
- **معالجة الأخطاء**: نظام محسن لمعالجة وتسجيل الأخطاء
- **أمان محسن**: تحسينات أمنية في قاعدة البيانات والاستعلامات
- **توافق أفضل**: توافق محسن مع المتصفحات المختلفة

## 🚀 الميزات الأساسية

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- عرض تفاصيل العملاء مع إحصائيات الطلبيات
- البحث والفلترة المتقدمة
- ربط العملاء بالطلبيات

### 📦 إدارة الطلبيات
- إنشاء طلبيات جديدة مع تفاصيل المنتجات
- رفع صور المنتجات
- تحديد القياسات (العرض، الارتفاع، العمق)
- تتبع حالة الطلبيات (معلقة، قيد التنفيذ، مكتملة، ملغية)
- حساب الأسعار والإجماليات

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية
- إحصائيات سريعة في شريط التنقل
- تقارير العملاء والطلبيات
- إحصائيات المبيعات

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- تأثيرات بصرية وانتقالات سلسة
- إشعارات تفاعلية

## 🛠️ متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبة PDO لـ PHP

## 📥 التثبيت

1. **رفع الملفات**: ارفع جميع الملفات إلى مجلد الويب
2. **إعداد قاعدة البيانات**: 
   - أنشئ قاعدة بيانات جديدة
   - حدث إعدادات الاتصال في `config/config.php`
3. **الأذونات**: تأكد من أذونات الكتابة لمجلدات `uploads/` و `logs/`
4. **الوصول**: افتح `index.php` في المتصفح

## 🔧 الإعدادات

### قاعدة البيانات
```php
// في config/config.php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'widdx_oms');
```

### إعدادات النظام
```php
define('SYSTEM_NAME', 'WIDDX OMS');
define('SYSTEM_VERSION', '2.1.0');
define('DEBUG_MODE', false); // للإنتاج
```

## 📝 الاستخدام

### إضافة عميل جديد
1. انتقل إلى "إضافة عميل"
2. أدخل بيانات العميل
3. احفظ البيانات

### إنشاء طلبية جديدة
1. انتقل إلى "إضافة طلبية"
2. اختر العميل أو أضف عميل جديد
3. أضف تفاصيل المنتجات والقياسات
4. ارفع صور المنتجات (اختياري)
5. احفظ الطلبية

### متابعة الطلبيات
1. انتقل إلى "عرض الطلبيات"
2. استخدم الفلاتر للبحث
3. حدث حالة الطلبيات
4. اعرض التفاصيل الكاملة

## 🔒 الأمان

- استعلامات محضرة (Prepared Statements)
- تشفير كلمات المرور
- حماية من SQL Injection
- تنظيف البيانات المدخلة
- حماية الملفات الحساسة

## 🐛 استكشاف الأخطاء

### ملفات السجلات
- `logs/database_errors.log`: أخطاء قاعدة البيانات
- `logs/system.log`: سجل النظام العام

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تحقق من إعدادات `config/config.php`
2. **مشاكل في رفع الصور**: تحقق من أذونات مجلد `uploads/`
3. **مشاكل في العرض**: تحقق من تحميل ملفات CSS و JS

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الموقع: www.widdx.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**WIDDX OMS v2.1.0** - نظام إدارة الطلبيات المحسن 🚀
