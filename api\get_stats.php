<?php
/**
 * API للإحصائيات العامة
 * يوفر بيانات الإحصائيات للوحة التحكم
 */

// إعداد الرؤوس
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// تحميل النظام
require_once __DIR__ . '/../config/autoload.php';

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }

    // إنشاء مثيل من قاعدة البيانات
    $db = getDatabase();

    // جلب الإحصائيات الأساسية
    $stats = [];

    // إجمالي العملاء
    $db->query("SELECT COUNT(*) as total FROM customers");
    $result = $db->single();
    $stats['total_customers'] = (int)($result['total'] ?? 0);

    // إجمالي الطلبيات
    $db->query("SELECT COUNT(*) as total FROM orders");
    $result = $db->single();
    $stats['total_orders'] = (int)($result['total'] ?? 0);

    // الطلبيات حسب الحالة
    $db->query("
        SELECT 
            status,
            COUNT(*) as count
        FROM orders 
        GROUP BY status
    ");
    $statusResults = $db->resultset();
    
    $stats['orders_by_status'] = [
        'pending' => 0,
        'processing' => 0,
        'completed' => 0,
        'cancelled' => 0
    ];

    foreach ($statusResults as $row) {
        $stats['orders_by_status'][$row['status']] = (int)$row['count'];
    }

    // إجمالي المبيعات
    $db->query("SELECT SUM(total_amount) as total FROM orders WHERE status = 'completed'");
    $result = $db->single();
    $stats['total_sales'] = (float)($result['total'] ?? 0);

    // متوسط قيمة الطلبية
    $stats['average_order_value'] = $stats['total_orders'] > 0 ? 
        round($stats['total_sales'] / $stats['orders_by_status']['completed'], 2) : 0;

    // إحصائيات الشهر الحالي
    $currentMonth = date('Y-m');
    $db->query("
        SELECT 
            COUNT(*) as orders_count,
            SUM(total_amount) as sales_amount
        FROM orders 
        WHERE DATE_FORMAT(created_at, '%Y-%m') = :month
    ");
    $db->bind(':month', $currentMonth);
    $monthResult = $db->single();
    
    $stats['current_month'] = [
        'orders' => (int)($monthResult['orders_count'] ?? 0),
        'sales' => (float)($monthResult['sales_amount'] ?? 0)
    ];

    // إحصائيات الأسبوع الحالي
    $weekStart = date('Y-m-d', strtotime('monday this week'));
    $db->query("
        SELECT 
            COUNT(*) as orders_count,
            SUM(total_amount) as sales_amount
        FROM orders 
        WHERE created_at >= :week_start
    ");
    $db->bind(':week_start', $weekStart);
    $weekResult = $db->single();
    
    $stats['current_week'] = [
        'orders' => (int)($weekResult['orders_count'] ?? 0),
        'sales' => (float)($weekResult['sales_amount'] ?? 0)
    ];

    // أحدث الطلبيات
    $db->query("
        SELECT 
            o.id,
            o.created_at,
            o.status,
            o.total_amount,
            c.name as customer_name
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $stats['recent_orders'] = $db->resultset();

    // أحدث العملاء
    $db->query("
        SELECT 
            id,
            name,
            email,
            phone,
            created_at
        FROM customers
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stats['recent_customers'] = $db->resultset();

    // إعداد الاستجابة
    $response = [
        'success' => true,
        'data' => $stats,
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s')
    ];

    // إرسال الاستجابة
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

} catch (PDOException $e) {
    // خطأ في قاعدة البيانات
    $response = [
        'success' => false,
        'message' => 'خطأ في الاتصال بقاعدة البيانات',
        'error_code' => 'DB_ERROR',
        'error' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : null,
        'timestamp' => time()
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

    // تسجيل الخطأ
    error_log("Get stats DB error: " . $e->getMessage());

} catch (Exception $e) {
    // خطأ عام
    $response = [
        'success' => false,
        'message' => 'حدث خطأ في جلب الإحصائيات',
        'error_code' => 'GENERAL_ERROR',
        'error' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : null,
        'timestamp' => time()
    ];

    // تحديد رمز الاستجابة بناءً على نوع الخطأ
    if (strpos($e->getMessage(), 'Method not allowed') !== false) {
        http_response_code(405);
    } else {
        http_response_code(500);
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);

    // تسجيل الخطأ
    error_log("Get stats API error: " . $e->getMessage());

} finally {
    // تنظيف الموارد
    if (isset($db)) {
        $db = null;
    }
}
?>
