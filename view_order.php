<?php
/**
 * صفحة عرض تفاصيل الطلبية - محدثة لاستخدام Layout الموحد
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

$db = getDatabase();

// التحقق من وجود معرف الطلبية
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: view_orders.php');
    exit;
}

$order_id = intval($_GET['id']);

// جلب معلومات الطلبية مع معلومات العميل
$db->query("SELECT o.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email, c.address as customer_address 
           FROM orders o 
           JOIN customers c ON o.customer_id = c.id 
           WHERE o.id = :id");
$db->bind(':id', $order_id);
$order = $db->single();

if (!$order) {
    header('Location: view_orders.php');
    exit;
}

// جلب عناصر الطلبية
$db->query("SELECT * FROM order_items WHERE order_id = :order_id ORDER BY id");
$db->bind(':order_id', $order_id);
$order_items = $db->resultset();

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('تفاصيل الطلبية #' . $order['id'])
    ->setPageDescription('عرض تفاصيل الطلبية رقم ' . $order['id'])
    ->setCurrentPage('view_order')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('عرض الطلبيات', 'view_orders.php', 'fas fa-list')
    ->addBreadcrumb('تفاصيل الطلبية #' . $order['id'], null, 'fas fa-receipt')
    ->showStatusBar(false);

// إضافة CSS مخصص للصفحة
$layout->addCustomStyles('
    .product-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
    }
    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            background-color: white !important;
        }
        .widdx-card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }
    }
');

// بدء Layout
$layout->startLayout();
?>

<div class="mt-4">
        <!-- أزرار الإجراءات -->
        <div class="row mb-3 no-print">
            <div class="col-12">
                <a href="view_orders.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للطلبيات
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>

        <div class="row">
            <!-- معلومات الطلبية -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-receipt"></i> تفاصيل الطلبية #<?php echo $order['id']; ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>تاريخ الطلبية:</strong><br>
                                <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>
                            </div>
                            <div class="col-md-6">
                                <strong>الحالة:</strong><br>
                                <?php
                                $status_class = '';
                                $status_text = '';
                                switch ($order['status']) {
                                    case 'pending':
                                        $status_class = 'bg-warning text-dark';
                                        $status_text = 'معلقة';
                                        break;
                                    case 'processing':
                                        $status_class = 'bg-info text-white';
                                        $status_text = 'قيد التنفيذ';
                                        break;
                                    case 'completed':
                                        $status_class = 'bg-success text-white';
                                        $status_text = 'مكتملة';
                                        break;
                                    case 'cancelled':
                                        $status_class = 'bg-danger text-white';
                                        $status_text = 'ملغية';
                                        break;
                                }
                                ?>
                                <span class="badge <?php echo $status_class; ?> status-badge">
                                    <?php echo $status_text; ?>
                                </span>
                            </div>
                        </div>
                        
                        <?php if (!empty($order['notes'])): ?>
                            <div class="mb-3">
                                <strong>ملاحظات الطلبية:</strong><br>
                                <div class="bg-light p-3 rounded">
                                    <?php echo nl2br(htmlspecialchars($order['notes'])); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- عناصر الطلبية -->
                        <h6 class="mb-3"><i class="fas fa-list"></i> عناصر الطلبية</h6>
                        <?php if (count($order_items) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>القياسات</th>
                                            <th>السعر</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($order_items as $item): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($item['product_image']) && file_exists($item['product_image'])): ?>
                                                            <img src="<?php echo $item['product_image']; ?>" 
                                                                 alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                                 class="product-image me-3">
                                                        <?php else: ?>
                                                            <div class="product-image bg-light d-flex align-items-center justify-content-center me-3">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                                            <?php if (!empty($item['notes'])): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($item['notes']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center"><?php echo $item['quantity']; ?></td>
                                                <td>
                                                    <?php if ($item['width'] || $item['height'] || $item['depth']): ?>
                                                        <?php if ($item['width']): ?>العرض: <?php echo $item['width']; ?> سم<br><?php endif; ?>
                                                        <?php if ($item['height']): ?>الارتفاع: <?php echo $item['height']; ?> سم<br><?php endif; ?>
                                                        <?php if ($item['depth']): ?>العمق: <?php echo $item['depth']; ?> سم<?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-end"><?php echo number_format($item['unit_price'], 2); ?> جنيه</td>
                                                <td class="text-end"><strong><?php echo number_format($item['total_price'], 2); ?> جنيه</strong></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="4" class="text-end">الإجمالي الكلي:</th>
                                            <th class="text-end text-success">
                                                <?php echo number_format($order['total_amount'], 2); ?> جنيه
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عناصر في هذه الطلبية</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- معلومات العميل -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user"></i> معلومات العميل</h5>
                    </div>
                    <div class="card-body">
                        <h6><?php echo htmlspecialchars($order['customer_name']); ?></h6>
                        
                        <?php if (!empty($order['customer_phone'])): ?>
                            <p class="mb-2">
                                <i class="fas fa-phone text-muted"></i> 
                                <?php echo htmlspecialchars($order['customer_phone']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($order['customer_email'])): ?>
                            <p class="mb-2">
                                <i class="fas fa-envelope text-muted"></i> 
                                <?php echo htmlspecialchars($order['customer_email']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($order['customer_address'])): ?>
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt text-muted"></i> 
                                <?php echo nl2br(htmlspecialchars($order['customer_address'])); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="mt-3 no-print">
                            <a href="manage_customers.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> عرض ملف العميل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // تحسين صفحة عرض الطلبية
    document.addEventListener("DOMContentLoaded", function() {
        // تحسين زر الطباعة
        const printBtn = document.querySelector("button[onclick*=print]");
        if (printBtn) {
            printBtn.addEventListener("click", function() {
                // إخفاء العناصر غير المطلوبة للطباعة
                const noPrintElements = document.querySelectorAll(".no-print");
                noPrintElements.forEach(el => el.style.display = "none");

                // طباعة
                window.print();

                // إعادة إظهار العناصر
                setTimeout(() => {
                    noPrintElements.forEach(el => el.style.display = "");
                }, 1000);
            });
        }

        // تحسين عرض الصور
        const productImages = document.querySelectorAll(".product-image");
        productImages.forEach(img => {
            img.addEventListener("click", function() {
                // إنشاء modal لعرض الصورة بحجم أكبر
                const modal = document.createElement("div");
                modal.className = "modal fade";
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">صورة المنتج</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${this.src}" class="img-fluid" alt="${this.alt}">
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();

                // حذف المودال بعد الإغلاق
                modal.addEventListener("hidden.bs.modal", function() {
                    document.body.removeChild(modal);
                });
            });

            // إضافة مؤشر للنقر
            img.style.cursor = "pointer";
            img.title = "انقر لعرض الصورة بحجم أكبر";
        });
    });
');

// إنهاء Layout
$layout->endLayout();
?>
