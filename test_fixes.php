<?php
/**
 * ملف اختبار سريع للتحقق من الإصلاحات
 */

// تحميل النظام
require_once 'config/autoload.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار الإصلاحات - WIDDX OMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet' 
          integrity='sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM' crossorigin='anonymous'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet' 
          integrity='sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==' crossorigin='anonymous'>
    <link href='assets/css/main.css' rel='stylesheet'>
    <link href='assets/css/components.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <div class='card widdx-card'>
        <div class='widdx-card-header text-center'>
            <h2><i class='fas fa-tools'></i> اختبار الإصلاحات</h2>
        </div>
        <div class='widdx-card-body'>";

echo "<h3><i class='fas fa-database'></i> 1. اختبار قاعدة البيانات</h3>";
try {
    $db = getDatabase();
    if ($db->isConnected()) {
        echo "<div class='alert widdx-alert-success'><i class='fas fa-check'></i> قاعدة البيانات: متصلة بنجاح</div>";
    } else {
        echo "<div class='alert widdx-alert-danger'><i class='fas fa-times'></i> قاعدة البيانات: فشل الاتصال</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert widdx-alert-danger'><i class='fas fa-times'></i> خطأ: " . $e->getMessage() . "</div>";
}

echo "<h3><i class='fas fa-file-code'></i> 2. اختبار ملفات CSS و JS</h3>";
$files = [
    'assets/css/main.css' => 'CSS الرئيسي',
    'assets/css/components.css' => 'CSS المكونات',
    'assets/js/main.js' => 'JavaScript الرئيسي',
    'assets/js/widdx-core.js' => 'JavaScript الأساسي'
];

foreach ($files as $file => $name) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<div class='alert widdx-alert-success'><i class='fas fa-check'></i> {$name}: موجود (" . number_format($size) . " بايت)</div>";
    } else {
        echo "<div class='alert widdx-alert-danger'><i class='fas fa-times'></i> {$name}: غير موجود</div>";
    }
}

echo "<h3><i class='fas fa-api'></i> 3. اختبار API</h3>";
$apis = [
    'api/quick_stats.php' => 'الإحصائيات السريعة',
    'api/get_stats.php' => 'الإحصائيات العامة'
];

foreach ($apis as $api => $name) {
    if (file_exists($api)) {
        echo "<div class='alert widdx-alert-success'><i class='fas fa-check'></i> {$name}: موجود</div>";
    } else {
        echo "<div class='alert widdx-alert-danger'><i class='fas fa-times'></i> {$name}: غير موجود</div>";
    }
}

echo "<h3><i class='fas fa-cogs'></i> 4. اختبار JavaScript</h3>";
echo "<div id='js-test-results'></div>";

echo "<h3><i class='fas fa-palette'></i> 5. اختبار CSS</h3>";
echo "<div class='row'>
    <div class='col-md-4'>
        <div class='widdx-card'>
            <div class='widdx-card-header'>
                <h5>بطاقة اختبار</h5>
            </div>
            <div class='widdx-card-body'>
                <p>هذه بطاقة اختبار للتأكد من عمل CSS</p>
                <button class='btn widdx-btn widdx-btn-primary'>زر اختبار</button>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='widdx-alert widdx-alert-info'>
            <i class='fas fa-info-circle'></i> رسالة اختبار
        </div>
    </div>
    <div class='col-md-4'>
        <table class='table widdx-table'>
            <thead>
                <tr>
                    <th>العمود 1</th>
                    <th>العمود 2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>بيانات 1</td>
                    <td>بيانات 2</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>";

echo "<h3><i class='fas fa-network-wired'></i> 6. اختبار API مباشر</h3>";
echo "<button id='test-quick-stats' class='btn widdx-btn widdx-btn-primary me-2'>
    <i class='fas fa-chart-line'></i> اختبار الإحصائيات السريعة
</button>";
echo "<button id='test-get-stats' class='btn widdx-btn widdx-btn-secondary'>
    <i class='fas fa-chart-bar'></i> اختبار الإحصائيات العامة
</button>";
echo "<div id='api-test-results' class='mt-3'></div>";

echo "<div class='mt-4 text-center'>
    <a href='index.php' class='btn widdx-btn widdx-btn-success'>
        <i class='fas fa-home'></i> العودة للصفحة الرئيسية
    </a>
</div>";

echo "        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/js/widdx-core.js'></script>
<script src='assets/js/main.js'></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const jsResults = document.getElementById('js-test-results');
    const apiResults = document.getElementById('api-test-results');
    
    // اختبار JavaScript
    let jsTests = [];
    
    // اختبار WIDDXCore
    if (typeof WIDDXCore !== 'undefined') {
        jsTests.push('<div class=\"alert widdx-alert-success\"><i class=\"fas fa-check\"></i> WIDDXCore: محمل بنجاح</div>');
    } else {
        jsTests.push('<div class=\"alert widdx-alert-danger\"><i class=\"fas fa-times\"></i> WIDDXCore: غير محمل</div>');
    }
    
    // اختبار widdxSystem
    if (typeof widdxSystem !== 'undefined') {
        jsTests.push('<div class=\"alert widdx-alert-success\"><i class=\"fas fa-check\"></i> widdxSystem: محمل بنجاح</div>');
    } else {
        jsTests.push('<div class=\"alert widdx-alert-warning\"><i class=\"fas fa-exclamation-triangle\"></i> widdxSystem: غير محمل</div>');
    }
    
    // اختبار updateQuickStats
    if (typeof updateQuickStats === 'function') {
        jsTests.push('<div class=\"alert widdx-alert-success\"><i class=\"fas fa-check\"></i> updateQuickStats: متوفر</div>');
    } else {
        jsTests.push('<div class=\"alert widdx-alert-danger\"><i class=\"fas fa-times\"></i> updateQuickStats: غير متوفر</div>');
    }
    
    jsResults.innerHTML = jsTests.join('');
    
    // اختبار API
    document.getElementById('test-quick-stats').addEventListener('click', async function() {
        apiResults.innerHTML = '<div class=\"alert widdx-alert-info\"><i class=\"fas fa-spinner fa-spin\"></i> جاري اختبار الإحصائيات السريعة...</div>';
        
        try {
            const response = await fetch('api/quick_stats.php');
            const data = await response.json();
            
            if (data.success) {
                apiResults.innerHTML = '<div class=\"alert widdx-alert-success\"><i class=\"fas fa-check\"></i> الإحصائيات السريعة: تعمل بنجاح<br><small>طلبيات اليوم: ' + (data.stats.today_orders || 0) + '</small></div>';
            } else {
                apiResults.innerHTML = '<div class=\"alert widdx-alert-warning\"><i class=\"fas fa-exclamation-triangle\"></i> الإحصائيات السريعة: ' + (data.message || 'خطأ غير معروف') + '</div>';
            }
        } catch (error) {
            apiResults.innerHTML = '<div class=\"alert widdx-alert-danger\"><i class=\"fas fa-times\"></i> خطأ في الإحصائيات السريعة: ' + error.message + '</div>';
        }
    });
    
    document.getElementById('test-get-stats').addEventListener('click', async function() {
        apiResults.innerHTML = '<div class=\"alert widdx-alert-info\"><i class=\"fas fa-spinner fa-spin\"></i> جاري اختبار الإحصائيات العامة...</div>';
        
        try {
            const response = await fetch('api/get_stats.php');
            const data = await response.json();
            
            if (data.success) {
                apiResults.innerHTML = '<div class=\"alert widdx-alert-success\"><i class=\"fas fa-check\"></i> الإحصائيات العامة: تعمل بنجاح<br><small>إجمالي العملاء: ' + (data.data.total_customers || 0) + '</small></div>';
            } else {
                apiResults.innerHTML = '<div class=\"alert widdx-alert-warning\"><i class=\"fas fa-exclamation-triangle\"></i> الإحصائيات العامة: ' + (data.message || 'خطأ غير معروف') + '</div>';
            }
        } catch (error) {
            apiResults.innerHTML = '<div class=\"alert widdx-alert-danger\"><i class=\"fas fa-times\"></i> خطأ في الإحصائيات العامة: ' + error.message + '</div>';
        }
    });
    
    console.log('✅ تم تحميل صفحة الاختبار بنجاح');
});
</script>
</body>
</html>";
?>
