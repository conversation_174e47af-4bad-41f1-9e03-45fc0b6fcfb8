<?php
/**
 * صفحة فحص النظام المحسنة - للتأكد من تكامل جميع المكونات
 */

// تحميل النظام
require_once 'config/database_fixed.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('فحص النظام')
    ->setPageDescription('فحص شامل لجميع مكونات النظام والتأكد من سلامتها')
    ->setCurrentPage('system_check')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('فحص النظام', null, 'fas fa-check-circle')
    ->showStatusBar(true);

// بدء Layout
$layout->startLayout();

// إجراء فحوصات النظام
$checks = [];

try {
    $db = getDatabase();
} catch (Exception $e) {
    $checks['database_connection'] = [
        'status' => 'error',
        'message' => 'خطأ في اتصال قاعدة البيانات: ' . $e->getMessage()
    ];
}

// فحص الملفات المطلوبة
$required_files = [
    'includes/header.php' => 'ملف الهيدر',
    'includes/footer.php' => 'ملف الفوتر',
    'includes/layout.php' => 'ملف Layout الموحد',
    'assets/css/main.css' => 'ملف CSS الرئيسي',
    'assets/js/main.js' => 'ملف JavaScript الرئيسي',
    'api/quick_stats.php' => 'API الإحصائيات السريعة',
    'config/database_fixed.php' => 'ملف تكوين قاعدة البيانات',
    'index.php' => 'الصفحة الرئيسية',
    'add_customer.php' => 'صفحة إضافة عميل',
    'add_order.php' => 'صفحة إضافة طلبية',
    'view_orders.php' => 'صفحة عرض الطلبيات',
    'manage_customers.php' => 'صفحة إدارة العملاء',
    'update_order_status.php' => 'ملف تحديث حالة الطلبية'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $checks["file_$file"] = [
            'status' => 'success',
            'message' => "$description موجود"
        ];
    } else {
        $checks["file_$file"] = [
            'status' => 'error',
            'message' => "$description غير موجود"
        ];
    }
}

// فحص المجلدات
$required_dirs = [
    'uploads' => 'مجلد الصور',
    'config' => 'مجلد الإعدادات',
    'assets' => 'مجلد الأصول',
    'assets/css' => 'مجلد CSS',
    'assets/js' => 'مجلد JavaScript',
    'includes' => 'مجلد الملفات المضمنة',
    'api' => 'مجلد API'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir) || $dir !== 'uploads') {
            $checks["dir_$dir"] = [
                'status' => 'success',
                'message' => "$description موجود" . ($dir === 'uploads' && is_writable($dir) ? ' وقابل للكتابة' : '')
            ];
        } else {
            $checks["dir_$dir"] = [
                'status' => 'warning',
                'message' => "$description موجود لكن غير قابل للكتابة"
            ];
        }
    } else {
        $checks["dir_$dir"] = [
            'status' => 'error',
            'message' => "$description غير موجود"
        ];
    }
}

// فحص قاعدة البيانات
if (isset($db)) {
    $checks['database_connection'] = [
        'status' => 'success',
        'message' => 'اتصال قاعدة البيانات يعمل بشكل صحيح'
    ];

    // فحص الجداول المطلوبة
    $required_tables = ['customers', 'orders', 'order_items'];
    foreach ($required_tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $db->single();
            $checks["table_$table"] = [
                'status' => 'success',
                'message' => "جدول $table موجود ويحتوي على {$result['count']} سجل"
            ];
        } catch (Exception $e) {
            $checks["table_$table"] = [
                'status' => 'error',
                'message' => "جدول $table غير موجود أو به مشكلة"
            ];
        }
    }
}

// فحص إعدادات PHP
$php_checks = [
    'file_uploads' => 'رفع الملفات',
    'session.auto_start' => 'بدء الجلسات التلقائي'
];

foreach ($php_checks as $setting => $description) {
    $value = ini_get($setting);
    $checks["php_$setting"] = [
        'status' => $value ? 'success' : 'warning',
        'message' => "$description: " . ($value ? 'مفعل' : 'غير مفعل')
    ];
}

// حساب الإحصائيات
$success_count = count(array_filter($checks, fn($check) => $check['status'] === 'success'));
$warning_count = count(array_filter($checks, fn($check) => $check['status'] === 'warning'));
$error_count = count(array_filter($checks, fn($check) => $check['status'] === 'error'));
$total_checks = count($checks);
?>

<div class="mt-4">
    <!-- ملخص النتائج -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="text-success"><?php echo $success_count; ?></h4>
                    <p class="mb-0">نجح</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h4 class="text-warning"><?php echo $warning_count; ?></h4>
                    <p class="mb-0">تحذير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                    <h4 class="text-danger"><?php echo $error_count; ?></h4>
                    <p class="mb-0">خطأ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <i class="fas fa-list fa-2x text-info mb-2"></i>
                    <h4 class="text-info"><?php echo $total_checks; ?></h4>
                    <p class="mb-0">إجمالي الفحوصات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج مفصلة -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-clipboard-check me-2"></i>نتائج فحص النظام</h5>
        </div>
        <div class="card-body">
            <?php foreach ($checks as $check_name => $check): ?>
                <div class="d-flex align-items-center mb-3 p-3 rounded border">
                    <?php if ($check['status'] === 'success'): ?>
                        <i class="fas fa-check-circle text-success fa-lg me-3"></i>
                    <?php elseif ($check['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle text-warning fa-lg me-3"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle text-danger fa-lg me-3"></i>
                    <?php endif; ?>

                    <div class="flex-grow-1">
                        <strong><?php echo str_replace(['_', 'file ', 'table ', 'dir ', 'php '], [' ', '', '', '', ''], $check_name); ?></strong>
                        <br>
                        <small class="text-muted"><?php echo $check['message']; ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- النتيجة النهائية -->
    <?php if ($error_count === 0): ?>
        <div class="alert alert-success text-center mt-4">
            <h4><i class="fas fa-check-circle me-2"></i>النظام جاهز للاستخدام!</h4>
            <p class="mb-3">جميع المكونات تعمل بشكل صحيح</p>
            <a href="index.php" class="btn btn-success me-2">
                <i class="fas fa-home me-1"></i>الانتقال للنظام
            </a>
        </div>
    <?php else: ?>
        <div class="alert alert-danger text-center mt-4">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>يوجد مشاكل تحتاج إصلاح</h4>
            <p class="mb-0">يرجى إصلاح الأخطاء المذكورة أعلاه</p>
        </div>
    <?php endif; ?>

    <!-- أزرار الإجراءات -->
    <div class="mt-4 text-center">
        <a href="index.php" class="btn btn-primary me-2">
            <i class="fas fa-home me-1"></i>العودة للرئيسية
        </a>
        <button onclick="location.reload()" class="btn btn-outline-secondary">
            <i class="fas fa-redo me-1"></i>إعادة الفحص
        </button>
    </div>
</div>

<?php
// إضافة JavaScript للتحديث التلقائي
$layout->addInlineJS('
    // تحديث الوقت كل ثانية
    setInterval(function() {
        const now = new Date();
        const timeString = now.toLocaleTimeString("ar-EG");
        document.title = "فحص النظام - " + timeString + " - WIDDX OMS";
    }, 1000);

    // إضافة تأثيرات بصرية
    document.addEventListener("DOMContentLoaded", function() {
        const cards = document.querySelectorAll(".card");
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = "0";
                card.style.transform = "translateY(20px)";
                card.style.transition = "all 0.5s ease";

                setTimeout(() => {
                    card.style.opacity = "1";
                    card.style.transform = "translateY(0)";
                }, 100);
            }, index * 100);
        });
    });
');

// إنهاء Layout
$layout->endLayout();
?>
