<?php
/**
 * صفحة اختبار ملفات الستايل والجافا سكريبت
 * تختبر جميع المكونات والتأثيرات البصرية
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('اختبار الستايل والجافا سكريبت')
    ->setPageDescription('صفحة اختبار شاملة لجميع مكونات الواجهة والتفاعلات')
    ->setCurrentPage('test_assets')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('اختبار الأصول', null, 'fas fa-vial')
    ->showStatusBar(false);

// بدء Layout
$layout->startLayout();
?>

<div class="container mt-4">
    <!-- اختبار البطاقات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h3><i class="fas fa-palette"></i> اختبار مكونات الواجهة</h3>
                </div>
                <div class="widdx-card-body">
                    <p>هذه الصفحة تختبر جميع مكونات الستايل والجافا سكريبت في النظام.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الأزرار -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-mouse-pointer"></i> اختبار الأزرار</h4>
                </div>
                <div class="widdx-card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn widdx-btn widdx-btn-primary w-100" onclick="testButton('primary')">
                                <i class="fas fa-check"></i> أساسي
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn widdx-btn widdx-btn-success w-100" onclick="testButton('success')">
                                <i class="fas fa-thumbs-up"></i> نجاح
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn widdx-btn widdx-btn-danger w-100" onclick="testButton('danger')">
                                <i class="fas fa-exclamation-triangle"></i> خطر
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn widdx-btn btn-outline-secondary w-100" onclick="testButton('secondary')">
                                <i class="fas fa-info-circle"></i> ثانوي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الرسائل -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-bell"></i> اختبار الرسائل</h4>
                </div>
                <div class="widdx-card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-info w-100" onclick="testMessage('info')">
                                <i class="fas fa-info-circle"></i> معلومات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-success w-100" onclick="testMessage('success')">
                                <i class="fas fa-check-circle"></i> نجاح
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-warning w-100" onclick="testMessage('warning')">
                                <i class="fas fa-exclamation-triangle"></i> تحذير
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-danger w-100" onclick="testMessage('danger')">
                                <i class="fas fa-times-circle"></i> خطأ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار مؤشر التحميل -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-spinner"></i> اختبار مؤشر التحميل</h4>
                </div>
                <div class="widdx-card-body">
                    <button class="btn widdx-btn widdx-btn-primary" onclick="testLoading()">
                        <i class="fas fa-play"></i> اختبار التحميل (3 ثوان)
                    </button>
                    <button class="btn widdx-btn btn-outline-secondary ms-2" onclick="testLoadingWithText()">
                        <i class="fas fa-text-width"></i> اختبار مع نص مخصص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار النماذج -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-edit"></i> اختبار النماذج</h4>
                </div>
                <div class="widdx-card-body">
                    <form onsubmit="testForm(event)">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حقل نصي</label>
                                <input type="text" class="form-control widdx-input" placeholder="أدخل نص هنا">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حقل بريد إلكتروني</label>
                                <input type="email" class="form-control widdx-input" placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">منطقة نص</label>
                            <textarea class="form-control widdx-input" rows="3" placeholder="أدخل نص طويل هنا"></textarea>
                        </div>
                        <button type="submit" class="btn widdx-btn widdx-btn-primary">
                            <i class="fas fa-paper-plane"></i> إرسال النموذج
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الجداول -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-table"></i> اختبار الجداول</h4>
                </div>
                <div class="widdx-card-body">
                    <table class="table widdx-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>أحمد محمد</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm widdx-btn widdx-btn-primary">تعديل</button>
                                    <button class="btn btn-sm btn-outline-danger">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>فاطمة علي</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-warning">معلق</span></td>
                                <td>
                                    <button class="btn btn-sm widdx-btn widdx-btn-primary">تعديل</button>
                                    <button class="btn btn-sm btn-outline-danger">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>محمد حسن</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">غير نشط</span></td>
                                <td>
                                    <button class="btn btn-sm widdx-btn widdx-btn-primary">تعديل</button>
                                    <button class="btn btn-sm btn-outline-danger">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار التنبيهات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-exclamation-circle"></i> اختبار التنبيهات</h4>
                </div>
                <div class="widdx-card-body">
                    <div class="alert widdx-alert widdx-alert-success">
                        <i class="fas fa-check-circle"></i> هذا تنبيه نجاح - تم تنفيذ العملية بنجاح!
                    </div>
                    <div class="alert widdx-alert widdx-alert-info">
                        <i class="fas fa-info-circle"></i> هذا تنبيه معلومات - معلومات مهمة للمستخدم.
                    </div>
                    <div class="alert widdx-alert widdx-alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> هذا تنبيه تحذير - انتبه لهذه المعلومة!
                    </div>
                    <div class="alert widdx-alert widdx-alert-danger">
                        <i class="fas fa-times-circle"></i> هذا تنبيه خطأ - حدث خطأ يجب إصلاحه.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card widdx-card">
                <div class="widdx-card-header">
                    <h4><i class="fas fa-info"></i> معلومات النظام</h4>
                </div>
                <div class="widdx-card-body">
                    <div id="system-info">
                        <p><strong>حالة WIDDXCore:</strong> <span id="widdx-core-status">جاري الفحص...</span></p>
                        <p><strong>حالة widdxSystem:</strong> <span id="widdx-system-status">جاري الفحص...</span></p>
                        <p><strong>إصدار Bootstrap:</strong> <span id="bootstrap-version">جاري الفحص...</span></p>
                        <p><strong>إصدار Font Awesome:</strong> <span id="fontawesome-version">جاري الفحص...</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // وظائف اختبار الأزرار
    function testButton(type) {
        console.log("Button test:", type);
        if (typeof showGlobalMessage === "function") {
            showGlobalMessage(`تم النقر على زر ${type}`, type === "danger" ? "warning" : "success");
        } else {
            alert(`تم النقر على زر ${type}`);
        }
    }

    // وظائف اختبار الرسائل
    function testMessage(type) {
        const messages = {
            info: "هذه رسالة معلومات",
            success: "تم تنفيذ العملية بنجاح",
            warning: "تحذير: يرجى الانتباه",
            danger: "خطأ: حدث خطأ في النظام"
        };
        
        if (typeof showGlobalMessage === "function") {
            showGlobalMessage(messages[type], type);
        } else {
            alert(messages[type]);
        }
    }

    // اختبار مؤشر التحميل
    function testLoading() {
        if (typeof showGlobalLoading === "function") {
            showGlobalLoading("جاري التحميل...");
            setTimeout(() => {
                if (typeof hideGlobalLoading === "function") {
                    hideGlobalLoading();
                    showGlobalMessage("تم الانتهاء من التحميل", "success");
                }
            }, 3000);
        } else {
            alert("مؤشر التحميل غير متوفر");
        }
    }

    function testLoadingWithText() {
        if (typeof showGlobalLoading === "function") {
            showGlobalLoading("جاري معالجة البيانات...");
            setTimeout(() => {
                if (typeof hideGlobalLoading === "function") {
                    hideGlobalLoading();
                    showGlobalMessage("تم معالجة البيانات بنجاح", "success");
                }
            }, 2000);
        } else {
            alert("مؤشر التحميل غير متوفر");
        }
    }

    // اختبار النموذج
    function testForm(event) {
        event.preventDefault();
        console.log("Form submitted");
        if (typeof showGlobalMessage === "function") {
            showGlobalMessage("تم إرسال النموذج بنجاح", "success");
        } else {
            alert("تم إرسال النموذج بنجاح");
        }
    }

    // فحص حالة النظام
    document.addEventListener("DOMContentLoaded", function() {
        // فحص WIDDXCore
        const coreStatus = document.getElementById("widdx-core-status");
        if (typeof WIDDXCore !== "undefined") {
            coreStatus.innerHTML = "<span class=\"text-success\">✅ محمل بنجاح</span>";
        } else {
            coreStatus.innerHTML = "<span class=\"text-danger\">❌ غير محمل</span>";
        }

        // فحص widdxSystem
        const systemStatus = document.getElementById("widdx-system-status");
        if (typeof widdxSystem !== "undefined" && widdxSystem !== null) {
            systemStatus.innerHTML = "<span class=\"text-success\">✅ محمل بنجاح</span>";
        } else {
            systemStatus.innerHTML = "<span class=\"text-danger\">❌ غير محمل</span>";
        }

        // فحص Bootstrap
        const bootstrapVersion = document.getElementById("bootstrap-version");
        if (typeof bootstrap !== "undefined") {
            bootstrapVersion.innerHTML = "<span class=\"text-success\">✅ محمل</span>";
        } else {
            bootstrapVersion.innerHTML = "<span class=\"text-danger\">❌ غير محمل</span>";
        }

        // فحص Font Awesome
        const faVersion = document.getElementById("fontawesome-version");
        const faTest = document.createElement("i");
        faTest.className = "fas fa-check";
        document.body.appendChild(faTest);
        const faStyles = window.getComputedStyle(faTest);
        if (faStyles.fontFamily.includes("Font Awesome")) {
            faVersion.innerHTML = "<span class=\"text-success\">✅ محمل</span>";
        } else {
            faVersion.innerHTML = "<span class=\"text-danger\">❌ غير محمل</span>";
        }
        document.body.removeChild(faTest);
    });
');

// إنهاء Layout
$layout->endLayout();
?>
