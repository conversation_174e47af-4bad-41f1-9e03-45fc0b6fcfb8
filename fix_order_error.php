<?php
/**
 * إصلاح شامل لجميع مشاكل إنشاء الطلبيات
 */

require_once 'config/database.php';

echo "<h2>🔧 إصلاح شامل لمشاكل إنشاء الطلبيات</h2>";

$errors_fixed = 0;
$warnings = 0;

try {
    // إنشاء اتصال مباشر بقاعدة البيانات
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<div class='alert alert-info'>🔍 بدء فحص شامل لقاعدة البيانات...</div>";

    // 1. التحقق من وجود الجداول الأساسية
    echo "<h3>📋 فحص الجداول الأساسية</h3>";

    $required_tables = ['customers', 'orders', 'order_items'];
    foreach ($required_tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "<p>✅ جدول $table موجود</p>";
        } else {
            echo "<p>❌ جدول $table غير موجود - يجب تشغيل install.php</p>";
            $errors_fixed++;
        }
    }

    // 2. فحص وإصلاح هيكل جدول order_items
    echo "<h3>🔧 فحص وإصلاح هيكل جدول order_items</h3>";

    // التحقق من وجود العمود product_image
    $result = $pdo->query("SHOW COLUMNS FROM order_items LIKE 'product_image'");

    if ($result->rowCount() == 0) {
        echo "<p>❌ العمود product_image غير موجود في جدول order_items</p>";
        echo "<p>🔧 إضافة العمود المفقود...</p>";

        try {
            $pdo->exec("ALTER TABLE order_items ADD COLUMN product_image VARCHAR(255) AFTER product_name");
            echo "<p>✅ تم إضافة العمود product_image بنجاح!</p>";
            $errors_fixed++;
        } catch (Exception $e) {
            echo "<p>❌ فشل في إضافة العمود: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>✅ العمود product_image موجود بالفعل في جدول order_items</p>";
    }

    // 3. فحص الأعمدة المطلوبة في جدول orders
    echo "<h3>📋 فحص جدول orders</h3>";

    $required_order_columns = ['id', 'customer_id', 'status', 'total_amount', 'notes', 'created_at'];
    $result = $pdo->query("DESCRIBE orders");
    $existing_columns = [];
    while ($row = $result->fetch()) {
        $existing_columns[] = $row['Field'];
    }

    foreach ($required_order_columns as $column) {
        if (in_array($column, $existing_columns)) {
            echo "<p>✅ العمود $column موجود في جدول orders</p>";
        } else {
            echo "<p>❌ العمود $column غير موجود في جدول orders</p>";
            $warnings++;
        }
    }

    // 4. إنشاء مجلد uploads إذا لم يكن موجوداً
    echo "<h3>📁 فحص مجلدات النظام</h3>";

    $upload_dir = __DIR__ . '/uploads';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p>✅ تم إنشاء مجلد uploads</p>";
            $errors_fixed++;
        } else {
            echo "<p>❌ فشل في إنشاء مجلد uploads</p>";
        }
    } else {
        echo "<p>✅ مجلد uploads موجود</p>";
    }

    // 5. التحقق من صلاحيات الكتابة
    if (is_writable($upload_dir)) {
        echo "<p>✅ مجلد uploads قابل للكتابة</p>";
    } else {
        echo "<p>⚠️ مجلد uploads غير قابل للكتابة - قد تحتاج لتغيير الصلاحيات</p>";
        $warnings++;
    }

    // 6. عرض هيكل الجداول الحالي
    echo "<h3>📋 هيكل الجداول الحالي</h3>";

    echo "<h4>جدول orders:</h4><ul>";
    $result = $pdo->query("DESCRIBE orders");
    while ($row = $result->fetch()) {
        echo "<li><strong>" . $row['Field'] . "</strong> (" . $row['Type'] . ")</li>";
    }
    echo "</ul>";

    echo "<h4>جدول order_items:</h4><ul>";
    $result = $pdo->query("DESCRIBE order_items");
    while ($row = $result->fetch()) {
        echo "<li><strong>" . $row['Field'] . "</strong> (" . $row['Type'] . ")</li>";
    }
    echo "</ul>";

    // 7. اختبار إنشاء طلبية تجريبية
    echo "<h3>🧪 اختبار إنشاء طلبية تجريبية</h3>";

    // التحقق من وجود عملاء
    $customerResult = $pdo->query("SELECT COUNT(*) as count FROM customers");
    $customerCount = $customerResult->fetch()['count'];

    if ($customerCount > 0) {
        // جلب أول عميل
        $customerResult = $pdo->query("SELECT id FROM customers LIMIT 1");
        $customerId = $customerResult->fetch()['id'];

        // محاولة إنشاء طلبية تجريبية
        $pdo->beginTransaction();

        try {
            // إنشاء الطلبية
            $stmt = $pdo->prepare("INSERT INTO orders (customer_id, notes, status, total_amount) VALUES (?, ?, ?, ?)");
            $stmt->execute([$customerId, 'طلبية تجريبية للاختبار', 'pending', 100.00]);
            $orderId = $pdo->lastInsertId();

            // إضافة عنصر للطلبية
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $orderId,
                'منتج تجريبي',
                null,
                1,
                100.00,
                200.00,
                50.00,
                100.00,
                100.00,
                'عنصر تجريبي للاختبار'
            ]);

            // حذف الطلبية التجريبية
            $pdo->exec("DELETE FROM order_items WHERE order_id = $orderId");
            $pdo->exec("DELETE FROM orders WHERE id = $orderId");

            $pdo->commit();

            echo "<p>✅ اختبار إنشاء الطلبية نجح! النظام يعمل بشكل صحيح.</p>";

        } catch (Exception $e) {
            $pdo->rollBack();
            echo "<p>❌ فشل اختبار إنشاء الطلبية: " . $e->getMessage() . "</p>";
            $errors_fixed++;
        }

    } else {
        echo "<p>⚠️ لا توجد عملاء في قاعدة البيانات لإجراء الاختبار</p>";
        echo "<p>يمكنك إضافة عميل من <a href='add_customer.php'>صفحة إضافة عميل</a> ثم اختبار إنشاء طلبية</p>";
        $warnings++;
    }

    // 8. تنظيف سجلات الأخطاء القديمة
    echo "<h3>🧹 تنظيف سجلات الأخطاء</h3>";

    $log_file = __DIR__ . '/logs/database_errors.log';
    if (file_exists($log_file)) {
        // إنشاء نسخة احتياطية من السجل
        $backup_file = $log_file . '.backup.' . date('Y-m-d-H-i-s');
        if (copy($log_file, $backup_file)) {
            echo "<p>✅ تم إنشاء نسخة احتياطية من سجل الأخطاء</p>";
        }

        // مسح السجل الحالي
        file_put_contents($log_file, '');
        echo "<p>✅ تم مسح سجل الأخطاء القديم</p>";
        $errors_fixed++;
    }

    echo "<hr>";
    echo "<div class='alert alert-success'>";
    echo "<h3>🎉 تم الانتهاء من الإصلاح الشامل!</h3>";
    echo "<p><strong>ملخص الإصلاحات:</strong></p>";
    echo "<ul>";
    echo "<li>تم إصلاح $errors_fixed مشكلة</li>";
    echo "<li>تم العثور على $warnings تحذير</li>";
    echo "</ul>";
    echo "</div>";

    if ($errors_fixed > 0) {
        echo "<div class='alert alert-info'>";
        echo "<p>تم إصلاح المشاكل الأساسية. يمكنك الآن تجربة إنشاء طلبية جديدة.</p>";
        echo "</div>";
    }

    if ($warnings > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<p>هناك بعض التحذيرات التي قد تحتاج لمراجعتها، لكنها لا تمنع عمل النظام.</p>";
        echo "</div>";
    }

} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ في قاعدة البيانات:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>تأكد من:</strong></p>";
    echo "<ul>";
    echo "<li>أن خادم MySQL يعمل</li>";
    echo "<li>أن إعدادات قاعدة البيانات صحيحة في config/database.php</li>";
    echo "<li>أن قاعدة البيانات widdx_oms موجودة</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ خطأ عام:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح خطأ الطلبيات - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-secondary">العودة للرئيسية</a>
            <a href="add_order.php" class="btn btn-primary">تجربة إضافة طلبية</a>
        </div>
    </div>
</body>
</html>
