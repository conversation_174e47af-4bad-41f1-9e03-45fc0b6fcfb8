/**
 * ملف JavaScript الأساسي المحسن لنظام WIDDX OMS
 * يحتوي على الوظائف الأساسية والمساعدة
 */

// الوظائف العامة المحسنة
const WIDDXCore = {
    // إعدادات النظام
    config: {
        animationDuration: 300,
        loadingTimeout: 30000,
        messageTimeout: 5000,
        apiEndpoint: window.location.origin,
        debug: false
    },

    // عناصر DOM المهمة
    elements: {
        loading: null,
        messageContainer: null,
        body: document.body
    },

    // تهيئة النظام
    init() {
        this.createLoadingElement();
        this.createMessageContainer();
        this.setupGlobalEvents();
        this.enhanceUI();
        console.log('WIDDX Core System initialized');
    },

    // إنشاء عنصر التحميل
    createLoadingElement() {
        if (!this.elements.loading) {
            this.elements.loading = document.createElement('div');
            this.elements.loading.className = 'widdx-loading';
            this.elements.loading.style.display = 'none';
            this.elements.loading.innerHTML = `
                <div class="text-center text-white">
                    <div class="widdx-spinner mb-3"></div>
                    <div class="loading-text">جاري التحميل...</div>
                </div>
            `;
            document.body.appendChild(this.elements.loading);
        }
    },

    // إنشاء حاوي الرسائل
    createMessageContainer() {
        if (!this.elements.messageContainer) {
            this.elements.messageContainer = document.createElement('div');
            this.elements.messageContainer.className = 'widdx-messages';
            this.elements.messageContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9998;
                max-width: 400px;
            `;
            document.body.appendChild(this.elements.messageContainer);
        }
    },

    // إعداد الأحداث العامة
    setupGlobalEvents() {
        // تحسين النماذج
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM' && !form.hasAttribute('data-no-enhance')) {
                this.enhanceForm(form);
            }
        });

        // تحسين الروابط
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[data-confirm]');
            if (link) {
                e.preventDefault();
                this.confirmAction(link.getAttribute('data-confirm'), () => {
                    window.location.href = link.href;
                });
            }
        });

        // إغلاق الرسائل تلقائياً
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('widdx-message-close')) {
                this.hideMessage(e.target.closest('.widdx-message'));
            }
        });
    },

    // تحسين واجهة المستخدم
    enhanceUI() {
        // إضافة كلاسات محسنة للعناصر
        document.querySelectorAll('.card').forEach(card => {
            if (!card.classList.contains('widdx-card')) {
                card.classList.add('widdx-card');
            }
        });

        document.querySelectorAll('.btn').forEach(btn => {
            if (!btn.classList.contains('widdx-btn')) {
                btn.classList.add('widdx-btn');
            }
        });

        document.querySelectorAll('.form-control').forEach(input => {
            if (!input.classList.contains('widdx-input')) {
                input.classList.add('widdx-input');
            }
        });

        document.querySelectorAll('.table').forEach(table => {
            if (!table.classList.contains('widdx-table')) {
                table.classList.add('widdx-table');
            }
        });
    },

    // إظهار مؤشر التحميل
    showLoading(text = 'جاري التحميل...') {
        if (this.elements.loading) {
            const textElement = this.elements.loading.querySelector('.loading-text');
            if (textElement) {
                textElement.textContent = text;
            }
            this.elements.loading.style.display = 'flex';
        }
    },

    // إخفاء مؤشر التحميل
    hideLoading() {
        if (this.elements.loading) {
            this.elements.loading.style.display = 'none';
        }
    },

    // إظهار رسالة
    showMessage(text, type = 'info', duration = null) {
        const message = document.createElement('div');
        message.className = `widdx-message widdx-alert widdx-alert-${type}`;
        message.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${text}</span>
                <button type="button" class="btn-close widdx-message-close" aria-label="إغلاق"></button>
            </div>
        `;

        this.elements.messageContainer.appendChild(message);

        // تأثير الظهور
        setTimeout(() => {
            message.style.opacity = '1';
            message.style.transform = 'translateX(0)';
        }, 10);

        // إخفاء تلقائي
        const hideAfter = duration || this.config.messageTimeout;
        if (hideAfter > 0) {
            setTimeout(() => {
                this.hideMessage(message);
            }, hideAfter);
        }

        return message;
    },

    // إخفاء رسالة
    hideMessage(messageElement) {
        if (messageElement && messageElement.parentNode) {
            messageElement.style.opacity = '0';
            messageElement.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, this.config.animationDuration);
        }
    },

    // تأكيد الإجراء
    confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // تحسين النماذج
    enhanceForm(form) {
        const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitBtn && !submitBtn.hasAttribute('data-no-enhance')) {
            const originalText = submitBtn.textContent || submitBtn.value;
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'جاري الإرسال...';
            
            // إعادة تفعيل الزر بعد فترة (في حالة عدم إعادة تحميل الصفحة)
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }, 5000);
        }
    },

    // طلب AJAX محسن
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('Request failed:', error);
            this.showMessage('حدث خطأ في الاتصال بالخادم', 'danger');
            throw error;
        }
    },

    // تحديث حالة الطلبية
    async updateOrderStatus(orderId, status) {
        try {
            this.showLoading('جاري تحديث حالة الطلبية...');
            
            const response = await this.request('update_order_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `order_id=${orderId}&status=${status}&ajax=1`
            });

            this.hideLoading();

            if (response.success || response.includes('success')) {
                this.showMessage('تم تحديث حالة الطلبية بنجاح', 'success');
                return true;
            } else {
                this.showMessage('حدث خطأ في تحديث حالة الطلبية', 'danger');
                return false;
            }
        } catch (error) {
            this.hideLoading();
            this.showMessage('حدث خطأ في تحديث حالة الطلبية', 'danger');
            return false;
        }
    },

    // تحميل طلبيات العميل
    async loadCustomerOrders(customerId) {
        try {
            this.showLoading('جاري تحميل طلبيات العميل...');
            
            const response = await this.request(`get_customer_orders.php?customer_id=${customerId}`);
            
            this.hideLoading();

            const container = document.getElementById(`orders-container-${customerId}`);
            if (container) {
                container.innerHTML = response;
                container.style.display = 'block';
            }

            return response;
        } catch (error) {
            this.hideLoading();
            this.showMessage('حدث خطأ في تحميل طلبيات العميل', 'danger');
            return null;
        }
    }
};

// تهيئة النظام عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => WIDDXCore.init());
} else {
    WIDDXCore.init();
}

// جعل النظام متاحاً عالمياً
window.WIDDXCore = WIDDXCore;

// وظائف مساعدة سريعة للتوافق مع الكود القديم
window.showGlobalLoading = (text) => WIDDXCore.showLoading(text);
window.hideGlobalLoading = () => WIDDXCore.hideLoading();
window.showGlobalMessage = (text, type) => WIDDXCore.showMessage(text, type);
window.updateOrderStatus = (orderId, status) => WIDDXCore.updateOrderStatus(orderId, status);

// وظائف إضافية مطلوبة
window.loadCustomerOrders = (customerId) => WIDDXCore.loadCustomerOrders(customerId);

// وظيفة البحث المباشر
window.liveSearch = function(input, container, options = {}) {
    const searchTerm = input.value.toLowerCase();
    const minLength = options.minLength || 2;
    const highlight = options.highlight || false;

    if (searchTerm.length < minLength) {
        // إظهار جميع العناصر
        const items = document.querySelectorAll(`${container} .order-row, ${container} .customer-row`);
        items.forEach(item => {
            item.style.display = 'block';
            if (highlight) {
                item.innerHTML = item.innerHTML.replace(/<mark>/g, '').replace(/<\/mark>/g, '');
            }
        });
        return;
    }

    const items = document.querySelectorAll(`${container} .order-row, ${container} .customer-row`);
    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            item.style.display = 'block';
            if (highlight) {
                // تمييز النص المطابق
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                item.innerHTML = item.innerHTML.replace(regex, '<mark>$1</mark>');
            }
        } else {
            item.style.display = 'none';
        }
    });
};

// وظيفة تحديث الإحصائيات السريعة
window.updateQuickStats = async function() {
    try {
        const response = await fetch('api/quick_stats.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // تحديث العناصر في الصفحة
                const elements = {
                    'today-orders': data.stats?.today_orders || 0,
                    'today-customers': data.stats?.today_customers || 0,
                    'today-sales': (data.stats?.today_sales || 0) + ' ج.م'
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });
            }
        }
    } catch (error) {
        console.error('Error updating quick stats:', error);
    }
};
