<?php
/**
 * إعدادات مبسطة للنظام - بدون مشاكل الجلسة
 */

// منع الوصول المباشر للملف
if (!defined('WIDDX_OMS')) {
    define('WIDDX_OMS', true);
}

// إعدادات قاعدة البيانات
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');
if (!defined('DB_NAME')) define('DB_NAME', 'widdx_oms');

// إعدادات النظام العامة
if (!defined('SYSTEM_NAME')) define('SYSTEM_NAME', 'WIDDX OMS');
if (!defined('SYSTEM_VERSION')) define('SYSTEM_VERSION', '2.0.0');
if (!defined('SYSTEM_AUTHOR')) define('SYSTEM_AUTHOR', 'WIDDX Team');

// إعدادات المسارات
if (!defined('BASE_PATH')) define('BASE_PATH', dirname(__DIR__));
if (!defined('ASSETS_PATH')) define('ASSETS_PATH', BASE_PATH . '/assets');
if (!defined('UPLOADS_PATH')) define('UPLOADS_PATH', BASE_PATH . '/uploads');
if (!defined('LOGS_PATH')) define('LOGS_PATH', BASE_PATH . '/logs');
if (!defined('CLASSES_PATH')) define('CLASSES_PATH', BASE_PATH . '/classes');
if (!defined('INCLUDES_PATH')) define('INCLUDES_PATH', BASE_PATH . '/includes');

// إعدادات التطوير
if (!defined('DEBUG_MODE')) define('DEBUG_MODE', true);
if (!defined('LOG_ERRORS')) define('LOG_ERRORS', true);

// إعدادات المنطقة الزمنية
if (!defined('DEFAULT_TIMEZONE')) define('DEFAULT_TIMEZONE', 'Africa/Cairo');
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

/**
 * وظائف مساعدة أساسية
 */
if (!function_exists('sanitize')) {
    function sanitize($input) {
        if (is_array($input)) {
            return array_map('sanitize', $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('formatDate')) {
    function formatDate($date, $format = 'Y-m-d H:i:s') {
        if (empty($date)) return '';
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $date;
        }
    }
}

if (!function_exists('formatMoney')) {
    function formatMoney($amount, $currency = 'EGP') {
        return number_format($amount, 2) . ' ' . $currency;
    }
}

if (!function_exists('logError')) {
    function logError($message, $file = 'error.log') {
        if (!LOG_ERRORS) return;
        
        $logDir = LOGS_PATH;
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/' . $file;
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

if (!function_exists('redirect')) {
    function redirect($url, $statusCode = 302) {
        header("Location: {$url}", true, $statusCode);
        exit;
    }
}

if (!function_exists('isAjaxRequest')) {
    function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * إدارة الجلسات المبسطة
 */
if (!function_exists('startSession')) {
    function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
}

if (!function_exists('getSession')) {
    function getSession($key, $default = null) {
        startSession();
        return $_SESSION[$key] ?? $default;
    }
}

if (!function_exists('setSession')) {
    function setSession($key, $value) {
        startSession();
        $_SESSION[$key] = $value;
    }
}

if (!function_exists('deleteSession')) {
    function deleteSession($key) {
        startSession();
        unset($_SESSION[$key]);
    }
}

/**
 * إنشاء المجلدات المطلوبة
 */
function createRequiredDirectories() {
    $requiredDirs = [
        UPLOADS_PATH,
        LOGS_PATH,
        ASSETS_PATH . '/css',
        ASSETS_PATH . '/js',
        ASSETS_PATH . '/images'
    ];
    
    foreach ($requiredDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // إنشاء ملف .htaccess لحماية مجلد logs
    $htaccessContent = "Order Deny,Allow\nDeny from all";
    $htaccessPath = LOGS_PATH . '/.htaccess';
    
    if (!file_exists($htaccessPath)) {
        file_put_contents($htaccessPath, $htaccessContent);
    }
    
    // إنشاء ملف index.php فارغ في المجلدات الحساسة
    $protectedDirs = [LOGS_PATH, UPLOADS_PATH];
    foreach ($protectedDirs as $dir) {
        $indexFile = $dir . '/index.php';
        if (!file_exists($indexFile)) {
            file_put_contents($indexFile, '<?php header("HTTP/1.0 403 Forbidden"); exit("Access denied"); ?>');
        }
    }
}

// إنشاء المجلدات المطلوبة
createRequiredDirectories();

// تسجيل بداية تشغيل النظام
logError("System initialized successfully", "system.log");
?>
