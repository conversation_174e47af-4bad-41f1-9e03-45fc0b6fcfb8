<?php
/**
 * صفحة إدارة العملاء - محدثة لاستخدام Layout الموحد
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('إدارة العملاء')
    ->setPageDescription('إدارة وتحرير بيانات العملاء في النظام')
    ->setCurrentPage('manage_customers')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('إدارة العملاء', null, 'fas fa-users-cog')
    ->showStatusBar(true);

$db = getDatabase();
$message = '';

// معالجة حذف عميل
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $customer_id = $_GET['delete'];
    
    // التحقق من وجود طلبيات للعميل
    $db->query("SELECT COUNT(*) as count FROM orders WHERE customer_id = :id");
    $db->bind(':id', $customer_id);
    $orders_count = $db->single()['count'];
    
    if ($orders_count > 0) {
        $message = '<div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> 
            لا يمكن حذف هذا العميل لأن لديه ' . $orders_count . ' طلبية. يرجى حذف الطلبيات أولاً أو نقلها لعميل آخر.
        </div>';
    } else {
        $db->query("DELETE FROM customers WHERE id = :id");
        $db->bind(':id', $customer_id);
        
        if ($db->execute()) {
            $message = '<div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم حذف العميل بنجاح!
            </div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في حذف العميل!</div>';
        }
    }
}

// معالجة تعديل عميل
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'edit_customer') {
    $customer_id = intval($_POST['customer_id']);
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $address = trim($_POST['address']);
    
    if (!empty($name)) {
        $db->query("UPDATE customers SET name = :name, phone = :phone, email = :email, address = :address, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $db->bind(':name', $name);
        $db->bind(':phone', $phone);
        $db->bind(':email', $email);
        $db->bind(':address', $address);
        $db->bind(':id', $customer_id);
        
        if ($db->execute()) {
            $message = '<div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم تحديث بيانات العميل بنجاح!
            </div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في تحديث بيانات العميل!</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
    }
}

// جلب جميع العملاء مع عدد طلبياتهم
$db->query("SELECT c.*,
           COUNT(o.id) as orders_count,
           SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
           SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_count,
           SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
           MAX(o.created_at) as last_order_date
           FROM customers c
           LEFT JOIN orders o ON c.id = o.customer_id
           GROUP BY c.id
           ORDER BY c.name");
$customers = $db->resultset();

// جلب بيانات العميل للتعديل
$edit_customer = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = $_GET['edit'];
    $db->query("SELECT * FROM customers WHERE id = :id");
    $db->bind(':id', $edit_id);
    $edit_customer = $db->single();
}

// إضافة CSS مخصص للصفحة
$layout->addCustomStyles('
    .customer-row {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    .customer-row:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .customer-active {
        border-left: 4px solid var(--success-color);
        background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
    }
    .customer-inactive {
        opacity: 0.7;
    }
    .edit-form {
        background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .btn-group .btn {
        transition: all 0.3s ease;
    }
    .btn-group .btn:hover {
        transform: scale(1.1);
        z-index: 2;
    }
');

// بدء Layout
$layout->startLayout();

// إظهار رسالة إذا وجدت
if (!empty($message)) {
    echo $message;
}
?>

<div class="mt-4">
        
        <!-- نموذج التعديل -->
        <?php if ($edit_customer): ?>
            <div class="edit-form">
                <h5><i class="fas fa-edit"></i> تعديل بيانات العميل: <?php echo htmlspecialchars($edit_customer['name']); ?></h5>
                <form method="POST" class="row g-3">
                    <input type="hidden" name="action" value="edit_customer">
                    <input type="hidden" name="customer_id" value="<?php echo $edit_customer['id']; ?>">
                    
                    <div class="col-md-6">
                        <label for="edit_name" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" 
                               value="<?php echo htmlspecialchars($edit_customer['name']); ?>" required>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="edit_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="edit_phone" name="phone" 
                               value="<?php echo htmlspecialchars($edit_customer['phone']); ?>">
                    </div>
                    
                    <div class="col-md-6">
                        <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="edit_email" name="email" 
                               value="<?php echo htmlspecialchars($edit_customer['email']); ?>">
                    </div>
                    
                    <div class="col-md-6">
                        <label for="edit_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="2"><?php echo htmlspecialchars($edit_customer['address']); ?></textarea>
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                        <a href="manage_customers.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- قائمة العملاء -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-users-cog"></i> إدارة العملاء</h5>
                <div>
                    <a href="add_customer.php" class="btn btn-light btn-sm me-2">
                        <i class="fas fa-user-plus"></i> إضافة عميل جديد
                    </a>
                    <span class="badge bg-light text-dark">
                        إجمالي العملاء: <?php echo count($customers); ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($customers) > 0): ?>
                    <?php foreach ($customers as $customer): ?>
                        <?php 
                        $active_orders = $customer['pending_count'] + $customer['processing_count'];
                        $is_active = $active_orders > 0;
                        ?>
                        <div class="customer-row <?php echo $is_active ? 'customer-active' : 'customer-inactive'; ?>">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h6 class="mb-1">
                                        <i class="fas fa-user <?php echo $is_active ? 'text-success' : 'text-muted'; ?>"></i>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                        <?php if ($is_active): ?>
                                            <span class="badge bg-success ms-2">نشط</span>
                                        <?php endif; ?>
                                    </h6>
                                    <?php if (!empty($customer['phone'])): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-phone"></i> <?php echo htmlspecialchars($customer['phone']); ?>
                                        </small>
                                    <?php endif; ?>
                                    <?php if (!empty($customer['email'])): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($customer['email']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-3">
                                    <?php if (!empty($customer['address'])): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> 
                                            <?php echo htmlspecialchars(substr($customer['address'], 0, 50)); ?>
                                            <?php if (strlen($customer['address']) > 50) echo '...'; ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-2 text-center">
                                    <div class="mb-1">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-shopping-cart"></i> <?php echo $customer['orders_count']; ?> طلبية
                                        </span>
                                    </div>
                                    <?php if ($active_orders > 0): ?>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-exclamation-circle"></i> <?php echo $active_orders; ?> نشطة
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($customer['last_order_date']): ?>
                                        <br><small class="text-muted">
                                            آخر طلبية: <?php echo date('Y-m-d', strtotime($customer['last_order_date'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="btn-group btn-group-sm w-100">
                                        <a href="add_order.php?customer_id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-success" title="إضافة طلبية">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="?edit=<?php echo $customer['id']; ?>" 
                                           class="btn btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="view_orders.php?customer_id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-info" title="عرض الطلبيات">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="?delete=<?php echo $customer['id']; ?>" 
                                           class="btn btn-danger" 
                                           onclick="return confirm('هل أنت متأكد من حذف العميل: <?php echo htmlspecialchars($customer['name']); ?>؟\n\nملاحظة: لا يمكن حذف العميل إذا كان لديه طلبيات.')"
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد عملاء مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                        <a href="add_customer.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> إضافة عميل جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // تحسين تجربة المستخدم لصفحة إدارة العملاء
    document.addEventListener("DOMContentLoaded", function() {
        // تحسين تأكيد الحذف
        const deleteLinks = document.querySelectorAll("a[href*=delete]");
        deleteLinks.forEach(link => {
            link.addEventListener("click", function(e) {
                e.preventDefault();

                const customerName = this.getAttribute("title") || "هذا العميل";
                const confirmMessage = `هل أنت متأكد من حذف العميل؟\\n\\nملاحظة: لا يمكن حذف العميل إذا كان لديه طلبيات.`;

                if (confirm(confirmMessage)) {
                    showGlobalLoading("جاري حذف العميل...");
                    window.location.href = this.href;
                }
            });
        });

        // تحسين نموذج التعديل
        const editForm = document.querySelector(".edit-form form");
        if (editForm) {
            editForm.addEventListener("submit", function(e) {
                const submitBtn = this.querySelector("button[type=submit]");
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>جاري الحفظ...";
                    showGlobalLoading("جاري حفظ التعديلات...");
                }
            });
        }

        // إضافة البحث المباشر
        const searchInput = document.createElement("input");
        searchInput.type = "text";
        searchInput.className = "form-control mb-3";
        searchInput.placeholder = "البحث في العملاء...";
        searchInput.addEventListener("input", function() {
            const searchTerm = this.value.toLowerCase();
            const customerRows = document.querySelectorAll(".customer-row");

            customerRows.forEach(row => {
                const customerName = row.querySelector("h6").textContent.toLowerCase();
                const customerPhone = row.querySelector("small")?.textContent.toLowerCase() || "";

                if (customerName.includes(searchTerm) || customerPhone.includes(searchTerm)) {
                    row.style.display = "block";
                    row.classList.add("fade-in");
                } else {
                    row.style.display = "none";
                    row.classList.remove("fade-in");
                }
            });
        });

        const cardBody = document.querySelector(".card .card-body");
        if (cardBody) {
            cardBody.insertBefore(searchInput, cardBody.firstChild);
        }

        // تحسين الأزرار
        const actionButtons = document.querySelectorAll(".btn-group .btn");
        actionButtons.forEach(btn => {
            btn.addEventListener("mouseenter", function() {
                this.style.transform = "scale(1.1)";
            });

            btn.addEventListener("mouseleave", function() {
                this.style.transform = "scale(1)";
            });
        });
    });
');

// إنهاء Layout
$layout->endLayout();
?>
