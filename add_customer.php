<?php
/**
 * صفحة إضافة عميل جديد - محدثة لاستخدام Layout الموحد
 */

// تحميل النظام
require_once 'config/database_fixed.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('إضافة عميل جديد')
    ->setPageDescription('إضافة عميل جديد إلى نظام إدارة الطلبيات')
    ->setCurrentPage('add_customer')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('إضافة عميل جديد', null, 'fas fa-user-plus')
    ->showStatusBar(false);

$message = '';

// معالجة إضافة عميل جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    try {
        if (class_exists('Customer')) {
            $customerManager = new Customer();
            $customerData = [
                'name' => $_POST['name'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? '',
                'address' => $_POST['address'] ?? ''
            ];

            $result = $customerManager->create($customerData);

            if ($result['success']) {
                $customer_id = $result['customer_id'];
                $message = '<div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ' . $result['message'] . '
                    <div class="mt-2">
                        <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                        </a>
                        <a href="add_customer.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-user-plus"></i> إضافة عميل آخر
                        </a>
                    </div>
                </div>';
            } else {
                $message = '<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ' . $result['message'] . '
                </div>';
            }
        } else {
            // استخدام الطريقة القديمة
            $db = getDatabase();
            $name = trim($_POST['name']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $address = trim($_POST['address']);

            if (!empty($name)) {
                $db->query("INSERT INTO customers (name, phone, email, address) VALUES (:name, :phone, :email, :address)");
                $db->bind(':name', $name);
                $db->bind(':phone', $phone);
                $db->bind(':email', $email);
                $db->bind(':address', $address);

                if ($db->execute()) {
                    $customer_id = $db->lastInsertId();
                    $message = '<div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> تم إضافة العميل بنجاح!
                        <div class="mt-2">
                            <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                            </a>
                            <a href="add_customer.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-user-plus"></i> إضافة عميل آخر
                            </a>
                        </div>
                    </div>';
                } else {
                    $message = '<div class="alert alert-danger">حدث خطأ في إضافة العميل!</div>';
                }
            } else {
                $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
            }
        }
    } catch (Exception $e) {
        $message = '<div class="alert alert-danger">حدث خطأ: ' . $e->getMessage() . '</div>';
    }
}

// بدء Layout
$layout->startLayout();

// إظهار رسالة إذا وجدت
if (!empty($message)) {
    echo $message;
}
?>

<div class="mt-4">

        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-user-plus"></i> إضافة عميل جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="أدخل اسم العميل الكامل"
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i> رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="مثال: 01234567890">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="مثال: <EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="أدخل العنوان التفصيلي"></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ العميل
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح مفيدة -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-lightbulb text-warning"></i> نصائح مفيدة</h6>
                        <ul class="mb-0">
                            <li>اسم العميل مطلوب، باقي البيانات اختيارية</li>
                            <li>يمكنك إضافة طلبية مباشرة بعد حفظ العميل</li>
                            <li>تأكد من صحة رقم الهاتف للتواصل</li>
                            <li>العنوان مهم لتسليم الطلبيات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // تحسين تجربة المستخدم
    document.addEventListener("DOMContentLoaded", function() {
        // تركيز على حقل الاسم
        const nameField = document.getElementById("name");
        if (nameField) {
            nameField.focus();
        }

        // تحسين إرسال النموذج
        const form = document.querySelector("form");
        if (form) {
            form.addEventListener("submit", function(e) {
                const submitBtn = form.querySelector("button[type=submit]");
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>جاري الحفظ...";

                    // إظهار مؤشر التحميل العام
                    showGlobalLoading("جاري حفظ بيانات العميل...");
                }
            });

            // التحقق من صحة البيانات
            form.setAttribute("data-validate", "true");
        }

        // تحسين حقول الإدخال
        const inputs = form.querySelectorAll("input, textarea");
        inputs.forEach(input => {
            input.addEventListener("blur", function() {
                if (this.hasAttribute("required") && !this.value.trim()) {
                    this.classList.add("is-invalid");
                } else {
                    this.classList.remove("is-invalid");
                }
            });
        });
    });
');

// إنهاء Layout
$layout->endLayout();
?>
