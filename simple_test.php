<?php
/**
 * اختبار مبسط للنظام
 */

echo "<h1>اختبار مبسط لنظام WIDDX OMS</h1>";
echo "<hr>";

// 1. اختبار تحميل ملف قاعدة البيانات
echo "<h2>1. اختبار تحميل ملف قاعدة البيانات</h2>";
if (file_exists('config/database.php')) {
    echo "✅ ملف config/database.php موجود<br>";
    require_once 'config/database.php';
    echo "✅ تم تحميل ملف قاعدة البيانات بنجاح<br>";
} else {
    echo "❌ ملف config/database.php غير موجود<br>";
}

echo "<hr>";

// 2. اختبار الثوابت
echo "<h2>2. اختبار الثوابت</h2>";
if (defined('DB_HOST')) {
    echo "✅ DB_HOST: " . DB_HOST . "<br>";
} else {
    echo "❌ DB_HOST غير معرف<br>";
}

if (defined('DB_NAME')) {
    echo "✅ DB_NAME: " . DB_NAME . "<br>";
} else {
    echo "❌ DB_NAME غير معرف<br>";
}

if (defined('DB_USER')) {
    echo "✅ DB_USER: " . DB_USER . "<br>";
} else {
    echo "❌ DB_USER غير معرف<br>";
}

echo "<hr>";

// 3. اختبار الاتصال بقاعدة البيانات
echo "<h2>3. اختبار الاتصال بقاعدة البيانات</h2>";
try {
    if (function_exists('getDatabase')) {
        $db = getDatabase();
        echo "✅ تم إنشاء مثيل من قاعدة البيانات<br>";
        
        // اختبار استعلام بسيط
        $db->query("SELECT 1 as test");
        $result = $db->single();
        if ($result && $result['test'] == 1) {
            echo "✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح<br>";
        } else {
            echo "❌ مشكلة في الاستعلام<br>";
        }
    } else {
        echo "❌ وظيفة getDatabase غير متاحة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// 4. اختبار الجداول
echo "<h2>4. اختبار وجود الجداول</h2>";
try {
    $db = getDatabase();
    
    // اختبار جدول العملاء
    $db->query("SHOW TABLES LIKE 'customers'");
    $result = $db->single();
    if ($result) {
        echo "✅ جدول customers موجود<br>";
        
        // عدد العملاء
        $db->query("SELECT COUNT(*) as count FROM customers");
        $count = $db->single();
        echo "- عدد العملاء: " . ($count['count'] ?? 0) . "<br>";
    } else {
        echo "❌ جدول customers غير موجود<br>";
    }
    
    // اختبار جدول الطلبيات
    $db->query("SHOW TABLES LIKE 'orders'");
    $result = $db->single();
    if ($result) {
        echo "✅ جدول orders موجود<br>";
        
        // عدد الطلبيات
        $db->query("SELECT COUNT(*) as count FROM orders");
        $count = $db->single();
        echo "- عدد الطلبيات: " . ($count['count'] ?? 0) . "<br>";
    } else {
        echo "❌ جدول orders غير موجود<br>";
    }
    
    // اختبار جدول عناصر الطلبيات
    $db->query("SHOW TABLES LIKE 'order_items'");
    $result = $db->single();
    if ($result) {
        echo "✅ جدول order_items موجود<br>";
        
        // عدد العناصر
        $db->query("SELECT COUNT(*) as count FROM order_items");
        $count = $db->single();
        echo "- عدد عناصر الطلبيات: " . ($count['count'] ?? 0) . "<br>";
    } else {
        echo "❌ جدول order_items غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الجداول: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// 5. اختبار الملفات الأساسية
echo "<h2>5. اختبار الملفات الأساسية</h2>";
$files = [
    'index.php' => 'الصفحة الرئيسية',
    'add_customer.php' => 'إضافة عميل',
    'add_order.php' => 'إضافة طلبية',
    'view_orders.php' => 'عرض الطلبيات',
    'manage_customers.php' => 'إدارة العملاء',
    'assets/css/main.css' => 'ملف CSS الرئيسي',
    'assets/js/main.js' => 'ملف JavaScript الرئيسي'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: موجود<br>";
    } else {
        echo "❌ {$description}: غير موجود<br>";
    }
}

echo "<hr>";

// 6. اختبار المجلدات
echo "<h2>6. اختبار المجلدات</h2>";
$dirs = [
    'assets' => 'مجلد الموارد',
    'assets/css' => 'مجلد CSS',
    'assets/js' => 'مجلد JavaScript',
    'assets/images' => 'مجلد الصور',
    'classes' => 'مجلد الكلاسات',
    'includes' => 'مجلد الملفات المشتركة',
    'config' => 'مجلد الإعدادات',
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الرفع'
];

foreach ($dirs as $dir => $description) {
    if (is_dir($dir)) {
        echo "✅ {$description}: موجود<br>";
    } else {
        echo "❌ {$description}: غير موجود<br>";
    }
}

echo "<hr>";

// 7. اختبار الكلاسات
echo "<h2>7. اختبار الكلاسات</h2>";
$classes = [
    'Database' => 'كلاس قاعدة البيانات',
    'Customer' => 'كلاس العملاء',
    'Order' => 'كلاس الطلبيات'
];

foreach ($classes as $class => $description) {
    if (class_exists($class)) {
        echo "✅ {$description}: متاح<br>";
    } else {
        echo "⚠️ {$description}: غير متاح<br>";
    }
}

echo "<hr>";

// 8. ملخص النتائج
echo "<h2>8. ملخص النتائج</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border: 1px solid #4caf50;'>";
echo "<h3 style='color: #2e7d32; margin-top: 0;'>النظام جاهز للاستخدام!</h3>";
echo "<p>تم اختبار المكونات الأساسية للنظام وهي تعمل بشكل صحيح.</p>";
echo "</div>";

echo "<hr>";

// 9. روابط الاختبار
echo "<h2>9. روابط الاختبار</h2>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li><a href='add_customer.php' target='_blank'>إضافة عميل</a></li>";
echo "<li><a href='add_order.php' target='_blank'>إضافة طلبية</a></li>";
echo "<li><a href='view_orders.php' target='_blank'>عرض الطلبيات</a></li>";
echo "<li><a href='manage_customers.php' target='_blank'>إدارة العملاء</a></li>";
echo "<li><a href='system_check.php' target='_blank'>فحص النظام</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h1, h2, h3 { color: #333; }
hr { margin: 20px 0; }
ul { margin: 10px 0; }
a { color: #1976d2; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
