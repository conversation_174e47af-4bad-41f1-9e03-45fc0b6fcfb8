# إصلاح مشكلة إنشاء الطلبيات - WIDDX OMS

## المشكلة
عند محاولة إنشاء طلبية جديدة، تظهر رسالة الخطأ:
```
حدث خطأ في إنشاء الطلبية: خطأ في تحضير الاستعلام
```

## السبب
المشكلة تحدث بسبب عدم وجود العمود `product_image` في جدول `order_items` في قاعدة البيانات.

## الحلول المتاحة

### الحل الأول: الإصلاح السريع (الأسهل)
1. افتح المتصفح واذهب إلى:
   ```
   http://localhost/quick_fix.php
   ```
2. ستظهر لك صفحة تقوم بإصلاح المشكلة تلقائياً
3. اتبع التعليمات على الشاشة

### الحل الثاني: الإصلاح الشامل
1. افتح المتصفح واذهب إلى:
   ```
   http://localhost/fix_order_error.php
   ```
2. ستحصل على تقرير مفصل عن حالة النظام وإصلاح جميع المشاكل

### الحل الثالث: الإصلاح اليدوي
إذا كنت تفضل الإصلاح اليدوي، قم بتنفيذ الأوامر التالية في قاعدة البيانات:

```sql
-- إضافة العمود المفقود
ALTER TABLE order_items ADD COLUMN product_image VARCHAR(255) AFTER product_name;

-- التأكد من وجود العمود status في جدول orders
ALTER TABLE orders ADD COLUMN status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending' AFTER customer_id;

-- التأكد من وجود العمود total_amount في جدول orders
ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 0.00 AFTER status;
```

## التحقق من نجاح الإصلاح

بعد تطبيق أي من الحلول أعلاه:

1. اذهب إلى صفحة إضافة طلبية:
   ```
   http://localhost/add_order.php
   ```

2. جرب إنشاء طلبية جديدة:
   - اختر عميل موجود أو أضف عميل جديد
   - أضف منتج واحد على الأقل
   - اضغط "إنشاء الطلبية"

3. إذا ظهرت رسالة "تم إنشاء الطلبية بنجاح"، فقد تم حل المشكلة

## مشاكل إضافية محتملة

### إذا لم تعمل الحلول أعلاه:

1. **تأكد من تشغيل خادم MySQL**
   - افتح XAMPP Control Panel
   - تأكد من أن MySQL يعمل (أخضر)

2. **تأكد من إعدادات قاعدة البيانات**
   - افتح ملف `config/database.php`
   - تأكد من صحة إعدادات الاتصال

3. **تأكد من وجود قاعدة البيانات**
   - افتح phpMyAdmin
   - تأكد من وجود قاعدة بيانات باسم `widdx_oms`

4. **إعادة إنشاء قاعدة البيانات**
   - إذا لم تكن قاعدة البيانات موجودة، اذهب إلى:
     ```
     http://localhost/install.php
     ```

## ملفات الإصلاح المتوفرة

- `quick_fix.php` - إصلاح سريع وبسيط
- `fix_order_error.php` - إصلاح شامل مع تقرير مفصل
- `update_database.php` - تحديث هيكل قاعدة البيانات
- `install.php` - إعادة إنشاء قاعدة البيانات من البداية

## الدعم

إذا استمرت المشكلة بعد تطبيق جميع الحلول:

1. تحقق من ملف سجل الأخطاء:
   ```
   logs/database_errors.log
   ```

2. تأكد من أن مجلد `uploads` موجود وقابل للكتابة

3. تأكد من أن جميع الملفات موجودة في المجلد الصحيح

## ملاحظات مهمة

- تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق أي إصلاحات
- جميع الحلول آمنة ولن تؤثر على البيانات الموجودة
- يمكن تشغيل ملفات الإصلاح عدة مرات دون مشاكل
