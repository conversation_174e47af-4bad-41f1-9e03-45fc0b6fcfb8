<?php
/**
 * ملف قاعدة البيانات المحسن والمصحح
 */

// تحميل الإعدادات المبسطة
require_once __DIR__ . '/simple_config.php';

// كلاس قاعدة البيانات الأساسي
class Database_Legacy {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $dbh;
    private $stmt;
    private $error;

    public function __construct() {
        // إعداد DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';
        
        // إعداد الخيارات
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        );

        // إنشاء اتصال PDO
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            logError("Database connection failed: " . $e->getMessage());
            if (DEBUG_MODE) {
                echo $this->error;
            }
        }
    }

    // تحضير الاستعلام
    public function query($query) {
        $this->stmt = $this->dbh->prepare($query);
    }

    // ربط القيم
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // تنفيذ الاستعلام
    public function execute() {
        return $this->stmt->execute();
    }

    // الحصول على النتائج
    public function resultset() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // الحصول على نتيجة واحدة
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }

    // عدد الصفوف
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // آخر ID مدرج
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }

    // بدء المعاملة
    public function beginTransaction() {
        return $this->dbh->beginTransaction();
    }

    // تأكيد المعاملة
    public function commit() {
        return $this->dbh->commit();
    }

    // إلغاء المعاملة
    public function rollBack() {
        return $this->dbh->rollBack();
    }

    // إغلاق الاتصال
    public function close() {
        $this->dbh = null;
    }
    
    // التحقق من وجود جدول
    public function tableExists($tableName) {
        try {
            $query = "SHOW TABLES LIKE ?";
            $stmt = $this->dbh->prepare($query);
            $stmt->execute([$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            logError("Table exists check failed: " . $e->getMessage());
            return false;
        }
    }
}

// تحميل الكلاسات المحسنة إذا كانت متاحة
if (file_exists(__DIR__ . '/../classes/Database.php') && !class_exists('Database')) {
    require_once __DIR__ . '/../classes/Database.php';
}

if (file_exists(__DIR__ . '/../classes/Customer.php') && !class_exists('Customer')) {
    require_once __DIR__ . '/../classes/Customer.php';
}

if (file_exists(__DIR__ . '/../classes/Order.php') && !class_exists('Order')) {
    require_once __DIR__ . '/../classes/Order.php';
}

// كلاس Database محسن مع Singleton
if (!class_exists('Database')) {
    class Database extends Database_Legacy {
        private static $instance = null;
        
        public static function getInstance() {
            if (self::$instance === null) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        // منع الاستنساخ
        private function __clone() {}
        
        // منع إلغاء التسلسل
        public function __wakeup() {
            throw new Exception("Cannot unserialize singleton");
        }
        
        // الحصول على معلومات قاعدة البيانات
        public function getDatabaseInfo() {
            $info = [];
            
            // معلومات الاتصال
            $info['host'] = DB_HOST;
            $info['database'] = DB_NAME;
            $info['charset'] = 'utf8mb4';
            
            try {
                // إصدار MySQL
                $result = $this->dbh->query("SELECT VERSION() as version")->fetch();
                $info['mysql_version'] = $result['version'];
                
                // حجم قاعدة البيانات
                $query = "SELECT 
                            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                          FROM information_schema.tables 
                          WHERE table_schema = ?";
                $stmt = $this->dbh->prepare($query);
                $stmt->execute([DB_NAME]);
                $result = $stmt->fetch();
                $info['size_mb'] = $result['size_mb'] ?? 0;
            } catch (Exception $e) {
                $info['mysql_version'] = 'غير متاح';
                $info['size_mb'] = 0;
            }
            
            return $info;
        }
    }
}

// وظيفة للحصول على مثيل قاعدة البيانات
if (!function_exists('getDatabase')) {
    function getDatabase() {
        if (class_exists('Database') && method_exists('Database', 'getInstance')) {
            return Database::getInstance();
        } else {
            return new Database_Legacy();
        }
    }
}

// وظيفة للتحقق من الاتصال
if (!function_exists('testDatabaseConnection')) {
    function testDatabaseConnection() {
        try {
            $db = getDatabase();
            $db->query("SELECT 1 as test");
            $result = $db->single();
            return $result && $result['test'] == 1;
        } catch (Exception $e) {
            logError("Database connection test failed: " . $e->getMessage());
            return false;
        }
    }
}

// وظيفة للحصول على إحصائيات سريعة
if (!function_exists('getQuickStats')) {
    function getQuickStats() {
        try {
            $db = getDatabase();
            $stats = [];
            
            // عدد العملاء
            $db->query("SELECT COUNT(*) as count FROM customers");
            $result = $db->single();
            $stats['customers'] = $result['count'] ?? 0;
            
            // عدد الطلبيات
            $db->query("SELECT COUNT(*) as count FROM orders");
            $result = $db->single();
            $stats['orders'] = $result['count'] ?? 0;
            
            // الطلبيات المعلقة
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
            $result = $db->single();
            $stats['pending'] = $result['count'] ?? 0;
            
            // الطلبيات قيد التنفيذ
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'");
            $result = $db->single();
            $stats['processing'] = $result['count'] ?? 0;
            
            // الطلبيات المكتملة
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'completed'");
            $result = $db->single();
            $stats['completed'] = $result['count'] ?? 0;
            
            return $stats;
        } catch (Exception $e) {
            logError("Quick stats failed: " . $e->getMessage());
            return [
                'customers' => 0,
                'orders' => 0,
                'pending' => 0,
                'processing' => 0,
                'completed' => 0
            ];
        }
    }
}
?>
