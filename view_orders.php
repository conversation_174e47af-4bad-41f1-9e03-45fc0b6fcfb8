<?php
/**
 * صفحة عرض الطلبيات - محدثة لاستخدام Layout الموحد
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('عرض الطلبيات')
    ->setPageDescription('عرض وإدارة جميع الطلبيات في النظام')
    ->setCurrentPage('view_orders')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('عرض الطلبيات', null, 'fas fa-list')
    ->showStatusBar(true);

$db = getDatabase();
$message = '';

// معالجة تحديث حالة الطلبية
if (isset($_POST['update_status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    
    $db->query("UPDATE orders SET status = :status WHERE id = :id");
    $db->bind(':status', $status);
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم تحديث حالة الطلبية بنجاح!</div>';
    }
}

// معالجة حذف طلبية
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $order_id = $_GET['delete'];
    
    $db->query("DELETE FROM orders WHERE id = :id");
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم حذف الطلبية بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-danger">حدث خطأ في حذف الطلبية!</div>';
    }
}

// جلب العملاء للفلترة
$db->query("SELECT id, name FROM customers ORDER BY name");
$customers = $db->resultset();

// جلب الطلبيات مع معلومات العملاء
$customer_filter = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

$where_conditions = [];
$params = [];

if ($customer_filter > 0) {
    $where_conditions[] = "o.customer_id = :customer_id";
    $params[':customer_id'] = $customer_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = :status";
    $params[':status'] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$db->query("SELECT o.*, c.name as customer_name, c.phone as customer_phone 
           FROM orders o 
           JOIN customers c ON o.customer_id = c.id 
           $where_clause 
           ORDER BY o.created_at DESC");

foreach ($params as $param => $value) {
    $db->bind($param, $value);
}

$orders = $db->resultset();

// بدء Layout
$layout->startLayout();

// إظهار رسالة إذا وجدت
if (!empty($message)) {
    showMessage($message, 'success');
}
?>

<div class="mt-4">

        <!-- فلاتر البحث -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلترة الطلبيات</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="customer_id" class="form-label">العميل</label>
                        <select class="form-select" id="customer_id" name="customer_id">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>" 
                                        <?php echo ($customer_filter == $customer['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                            <option value="processing" <?php echo ($status_filter == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                            <option value="completed" <?php echo ($status_filter == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="view_orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة الطلبيات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> قائمة الطلبيات</h5>
                <a href="add_order.php" class="btn btn-light btn-sm">
                    <i class="fas fa-plus"></i> إضافة طلبية جديدة
                </a>
            </div>
            <div class="card-body">
                <?php if (count($orders) > 0): ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="order-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-2">
                                        <i class="fas fa-receipt text-primary"></i> 
                                        طلبية رقم #<?php echo $order['id']; ?>
                                    </h6>
                                    <p class="mb-1">
                                        <i class="fas fa-user text-muted"></i> 
                                        <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                        <?php if (!empty($order['customer_phone'])): ?>
                                            - <i class="fas fa-phone text-muted"></i> <?php echo htmlspecialchars($order['customer_phone']); ?>
                                        <?php endif; ?>
                                    </p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>
                                    </small>
                                    
                                    <?php if (!empty($order['notes'])): ?>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-sticky-note"></i> 
                                                <?php echo htmlspecialchars($order['notes']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-4 text-end">
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($order['status']) {
                                        case 'pending':
                                            $status_class = 'bg-warning text-dark';
                                            $status_text = 'معلقة';
                                            break;
                                        case 'processing':
                                            $status_class = 'bg-info text-white';
                                            $status_text = 'قيد التنفيذ';
                                            break;
                                        case 'completed':
                                            $status_class = 'bg-success text-white';
                                            $status_text = 'مكتملة';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'bg-danger text-white';
                                            $status_text = 'ملغية';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?> status-badge mb-2">
                                        <?php echo $status_text; ?>
                                    </span>
                                    
                                    <?php if ($order['total_amount'] > 0): ?>
                                        <div class="mb-2">
                                            <strong class="text-success">
                                                <i class="fas fa-money-bill"></i> 
                                                <?php echo number_format($order['total_amount'], 2); ?> جنيه
                                            </strong>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="btn-group-vertical btn-group-sm w-100">
                                        <a href="view_order.php?id=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> عرض التفاصيل
                                        </a>
                                        
                                        <!-- نموذج تحديث الحالة -->
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <select name="status" class="form-select form-select-sm" 
                                                    onchange="this.form.submit()">
                                                <option value="pending" <?php echo ($order['status'] == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                                                <option value="processing" <?php echo ($order['status'] == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                                <option value="completed" <?php echo ($order['status'] == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                                                <option value="cancelled" <?php echo ($order['status'] == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                        
                                        <a href="?delete=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-danger btn-sm mt-1" 
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الطلبية؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبيات</h5>
                        <p class="text-muted">ابدأ بإضافة طلبية جديدة</p>
                        <a href="add_order.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة طلبية جديدة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // تحسين تجربة المستخدم لصفحة عرض الطلبيات
    document.addEventListener("DOMContentLoaded", function() {
        // تحسين نماذج تحديث الحالة
        const statusForms = document.querySelectorAll("form select[name=status]");
        statusForms.forEach(select => {
            select.addEventListener("change", function() {
                const form = this.closest("form");
                const orderId = form.querySelector("input[name=order_id]").value;
                const status = this.value;

                // تأكيد التغيير
                if (confirm("هل أنت متأكد من تغيير حالة الطلبية؟")) {
                    showGlobalLoading("جاري تحديث حالة الطلبية...");

                    // استخدام AJAX بدلاً من إعادة تحميل الصفحة
                    updateOrderStatus(orderId, status).then(success => {
                        if (success) {
                            // تحديث الواجهة محلياً
                            updateOrderStatusUI(orderId, status);
                        }
                        hideGlobalLoading();
                    });
                } else {
                    // إعادة القيمة السابقة
                    this.value = this.getAttribute("data-original-value") || "pending";
                }
            });

            // حفظ القيمة الأصلية
            select.setAttribute("data-original-value", select.value);
        });

        // تحسين روابط الحذف
        const deleteLinks = document.querySelectorAll("a[href*=delete]");
        deleteLinks.forEach(link => {
            link.addEventListener("click", function(e) {
                e.preventDefault();

                if (confirm("هل أنت متأكد من حذف هذه الطلبية؟\\n\\nهذا الإجراء لا يمكن التراجع عنه.")) {
                    showGlobalLoading("جاري حذف الطلبية...");
                    window.location.href = this.href;
                }
            });
        });

        // إضافة البحث المباشر
        const searchInput = document.createElement("input");
        searchInput.type = "text";
        searchInput.className = "form-control mb-3";
        searchInput.placeholder = "البحث في الطلبيات...";
        searchInput.addEventListener("input", function() {
            liveSearch(this, ".card-body", { minLength: 2, highlight: true });
        });

        const cardBody = document.querySelector(".card .card-body");
        if (cardBody) {
            cardBody.insertBefore(searchInput, cardBody.firstChild);
        }
    });

    // وظيفة تحديث واجهة حالة الطلبية
    function updateOrderStatusUI(orderId, status) {
        const orderItems = document.querySelectorAll(`[data-order-id="${orderId}"]`);
        const statusTexts = {
            "pending": { text: "معلقة", class: "bg-warning text-dark" },
            "processing": { text: "قيد التنفيذ", class: "bg-info text-white" },
            "completed": { text: "مكتملة", class: "bg-success text-white" },
            "cancelled": { text: "ملغية", class: "bg-danger text-white" }
        };

        orderItems.forEach(item => {
            const statusBadge = item.querySelector(".status-badge");
            if (statusBadge && statusTexts[status]) {
                statusBadge.className = `badge status-badge ${statusTexts[status].class}`;
                statusBadge.textContent = statusTexts[status].text;

                // تأثير بصري
                statusBadge.style.transform = "scale(1.1)";
                setTimeout(() => {
                    statusBadge.style.transform = "scale(1)";
                }, 200);
            }
        });
    }
');

// إنهاء Layout
$layout->endLayout();
?>
