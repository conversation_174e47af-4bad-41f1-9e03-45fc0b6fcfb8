    </main>
    <!-- نهاية المحتوى الرئيسي -->

    <!-- Footer محسن -->
    <footer class="bg-dark text-light mt-auto py-4 no-print">
        <div class="container">
            <div class="row g-4">
                <!-- معلومات النظام -->
                <div class="col-lg-4 col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-box fa-2x text-primary me-3"></i>
                        <div>
                            <h5 class="mb-0">WIDDX OMS</h5>
                            <small class="text-muted">نظام إدارة الطلبيات</small>
                        </div>
                    </div>
                    <p class="text-light-emphasis mb-3">
                        نظام متطور وشامل لإدارة العملاء والطلبيات مع واجهة سهلة الاستخدام
                        وميزات متقدمة لتحسين كفاءة العمل.
                    </p>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-outline-light btn-sm" title="دليل المستخدم">
                            <i class="fas fa-book"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm" title="الدعم الفني">
                            <i class="fas fa-headset"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm" title="تقرير مشكلة">
                            <i class="fas fa-bug"></i>
                        </a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="col-lg-2 col-md-6">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-link me-2"></i>روابط سريعة
                    </h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="index.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-home me-2 text-muted"></i> الرئيسية
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="add_customer.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-user-plus me-2 text-muted"></i> إضافة عميل
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="add_order.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-plus-circle me-2 text-muted"></i> إضافة طلبية
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="view_orders.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-list me-2 text-muted"></i> عرض الطلبيات
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="manage_customers.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-users-cog me-2 text-muted"></i> إدارة العملاء
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- أدوات وتقارير -->
                <div class="col-lg-3 col-md-6">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-tools me-2"></i>أدوات وتقارير
                    </h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="system_check.php" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-check-circle me-2 text-success"></i> فحص النظام
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" onclick="exportData('excel', 'orders')" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-file-excel me-2 text-success"></i> تصدير الطلبيات
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" onclick="exportData('excel', 'customers')" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-file-excel me-2 text-success"></i> تصدير العملاء
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#" onclick="printPage()" class="text-light text-decoration-none d-flex align-items-center">
                                <i class="fas fa-print me-2 text-info"></i> طباعة الصفحة
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- معلومات تقنية -->
                <div class="col-lg-3 col-md-6">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-info-circle me-2"></i>معلومات النظام
                    </h6>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">الإصدار:</span>
                            <span class="badge bg-primary">v2.0.0</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">آخر تحديث:</span>
                            <span><?php echo date('Y-m-d'); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">الخادم:</span>
                            <span><?php echo $_SERVER['SERVER_NAME'] ?? 'localhost'; ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">قاعدة البيانات:</span>
                            <span class="badge bg-info">MySQL</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">PHP:</span>
                            <span class="badge bg-warning text-dark"><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">الوقت:</span>
                            <span id="current-time"><?php echo date('H:i:s'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- خط فاصل -->
            <hr class="my-4 border-secondary">

            <!-- حقوق النشر والمعلومات الإضافية -->
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-light-emphasis">
                        <i class="fas fa-copyright me-1"></i>
                        <?php echo date('Y'); ?> WIDDX OMS. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex justify-content-md-end align-items-center gap-3">
                        <small class="text-muted">
                            تم التطوير بواسطة <strong class="text-primary">WIDDX Team</strong>
                        </small>
                        <div class="vr d-none d-md-block"></div>
                        <small class="text-muted">
                            <i class="fas fa-heart text-danger me-1"></i>
                            صُنع بحب في مصر
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- أزرار عائمة محسنة -->
    <div class="floating-buttons no-print">
        <!-- زر العودة لأعلى -->
        <button id="back-to-top" class="btn btn-primary rounded-circle shadow-lg"
                style="display: none; position: fixed; bottom: 20px; left: 20px; z-index: 1000; width: 50px; height: 50px;"
                onclick="scrollToTop()" title="العودة لأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>

        <!-- زر المساعدة -->
        <button id="help-button" class="btn btn-info rounded-circle shadow-lg"
                style="position: fixed; bottom: 80px; left: 20px; z-index: 1000; width: 50px; height: 50px;"
                onclick="toggleHelp()" title="المساعدة">
            <i class="fas fa-question"></i>
        </button>

        <!-- زر الإعدادات السريعة -->
        <button id="quick-settings" class="btn btn-secondary rounded-circle shadow-lg"
                style="position: fixed; bottom: 140px; left: 20px; z-index: 1000; width: 50px; height: 50px;"
                onclick="toggleQuickSettings()" title="الإعدادات السريعة">
            <i class="fas fa-cog"></i>
        </button>
    </div>

    <!-- نافذة المساعدة -->
    <div id="help-modal" class="modal fade" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-question-circle me-2"></i>مساعدة النظام
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-keyboard text-primary me-2"></i>اختصارات لوحة المفاتيح</h6>
                            <ul class="list-unstyled small">
                                <li><kbd>Ctrl + S</kbd> - حفظ</li>
                                <li><kbd>Ctrl + N</kbd> - جديد</li>
                                <li><kbd>Ctrl + P</kbd> - طباعة</li>
                                <li><kbd>Esc</kbd> - إغلاق النوافذ</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-mouse text-success me-2"></i>نصائح الاستخدام</h6>
                            <ul class="list-unstyled small">
                                <li>انقر مرتين لتحرير البيانات</li>
                                <li>استخدم الفلاتر للبحث السريع</li>
                                <li>احفظ عملك بانتظام</li>
                                <li>راجع الإشعارات دورياً</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <a href="#" class="btn btn-info">دليل المستخدم الكامل</a>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإعدادات السريعة -->
    <div id="quick-settings-modal" class="modal fade" tabindex="-1" aria-labelledby="quickSettingsLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title" id="quickSettingsLabel">
                        <i class="fas fa-cog me-2"></i>الإعدادات السريعة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="darkMode" onchange="toggleDarkMode()">
                        <label class="form-check-label" for="darkMode">الوضع الليلي</label>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="autoSave" checked onchange="toggleAutoSave()">
                        <label class="form-check-label" for="autoSave">الحفظ التلقائي</label>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="notifications" checked onchange="toggleNotifications()">
                        <label class="form-check-label" for="notifications">الإشعارات</label>
                    </div>
                    <div class="mb-3">
                        <label for="refreshInterval" class="form-label">فترة التحديث (ثانية)</label>
                        <select class="form-select" id="refreshInterval" onchange="updateRefreshInterval()">
                            <option value="30">30 ثانية</option>
                            <option value="60" selected>دقيقة واحدة</option>
                            <option value="300">5 دقائق</option>
                            <option value="0">يدوي</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">حفظ الإعدادات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات الخارجية -->
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>

    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>

    <!-- Additional JavaScript if provided -->
    <?php if (isset($additionalJS) && is_array($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo htmlspecialchars($js); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript for specific pages -->
    <?php if (isset($inlineJS) && !empty($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>

    <!-- Global JavaScript functions محسن -->
    <script>
        // متغيرات الإعدادات
        let userSettings = {
            darkMode: false,
            autoSave: true,
            notifications: true,
            refreshInterval: 60
        };

        // تحميل الإعدادات من localStorage
        function loadSettings() {
            const saved = localStorage.getItem('widdx_settings');
            if (saved) {
                userSettings = { ...userSettings, ...JSON.parse(saved) };
                applySettings();
            }
        }

        // تطبيق الإعدادات
        function applySettings() {
            // تطبيق الوضع الليلي
            if (userSettings.darkMode) {
                document.body.classList.add('dark-mode');
                document.getElementById('darkMode').checked = true;
            }

            // تطبيق باقي الإعدادات
            document.getElementById('autoSave').checked = userSettings.autoSave;
            document.getElementById('notifications').checked = userSettings.notifications;
            document.getElementById('refreshInterval').value = userSettings.refreshInterval;
        }

        // حفظ الإعدادات
        function saveSettings() {
            localStorage.setItem('widdx_settings', JSON.stringify(userSettings));
            showGlobalMessage('تم حفظ الإعدادات بنجاح', 'success', 2000);
            bootstrap.Modal.getInstance(document.getElementById('quick-settings-modal')).hide();
        }

        // إظهار/إخفاء زر العودة لأعلى مع تأثيرات محسنة
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            const backToTopButton = document.getElementById('back-to-top');
            const scrolled = window.pageYOffset;

            if (scrolled > 300) {
                backToTopButton.style.display = 'block';
                backToTopButton.style.opacity = '1';
                backToTopButton.style.transform = 'scale(1)';
            } else {
                backToTopButton.style.opacity = '0';
                backToTopButton.style.transform = 'scale(0.8)';
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    backToTopButton.style.display = 'none';
                }, 300);
            }
        });

        // وظيفة العودة لأعلى محسنة
        function scrollToTop() {
            const duration = 500;
            const start = window.pageYOffset;
            const startTime = performance.now();

            function animateScroll(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeInOutCubic = progress < 0.5
                    ? 4 * progress * progress * progress
                    : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;

                window.scrollTo(0, start * (1 - easeInOutCubic));

                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            }

            requestAnimationFrame(animateScroll);
        }

        // إظهار مؤشر التحميل العام مع رسالة
        function showGlobalLoading(message = 'جاري التحميل...') {
            const loadingElement = document.getElementById('global-loading');
            const messageElement = document.getElementById('loading-message');

            if (messageElement) {
                messageElement.textContent = message;
            }

            loadingElement.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // إخفاء مؤشر التحميل العام
        function hideGlobalLoading() {
            const loadingElement = document.getElementById('global-loading');
            loadingElement.style.display = 'none';
            document.body.style.overflow = '';
        }

        // وظيفة عرض رسالة عامة محسنة
        function showGlobalMessage(message, type = 'info', duration = 5000) {
            const container = document.getElementById('global-messages-container');
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-triangle',
                'warning': 'fas fa-exclamation-circle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-info-circle';

            const alertId = 'alert-' + Date.now();
            const alertHtml = `
                <div class="container mt-3">
                    <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show shadow-sm" role="alert">
                        <i class="${icon} me-2"></i> ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', alertHtml);

            // تأثير صوتي (اختياري)
            if (type === 'success') {
                playNotificationSound('success');
            } else if (type === 'error') {
                playNotificationSound('error');
            }

            // إزالة الرسالة تلقائياً
            if (duration > 0) {
                setTimeout(() => {
                    const alert = document.getElementById(alertId);
                    if (alert) {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 150);
                    }
                }, duration);
            }
        }

        // تشغيل صوت الإشعار
        function playNotificationSound(type) {
            if (!userSettings.notifications) return;

            // يمكن إضافة ملفات صوتية هنا
            // const audio = new Audio(`assets/sounds/${type}.mp3`);
            // audio.play().catch(() => {}); // تجاهل الأخطاء
        }

        // وظائف النوافذ المنبثقة
        function toggleHelp() {
            const modal = new bootstrap.Modal(document.getElementById('help-modal'));
            modal.show();
        }

        function toggleQuickSettings() {
            const modal = new bootstrap.Modal(document.getElementById('quick-settings-modal'));
            modal.show();
        }

        // وظائف الإعدادات
        function toggleDarkMode() {
            userSettings.darkMode = document.getElementById('darkMode').checked;
            document.body.classList.toggle('dark-mode', userSettings.darkMode);
        }

        function toggleAutoSave() {
            userSettings.autoSave = document.getElementById('autoSave').checked;
        }

        function toggleNotifications() {
            userSettings.notifications = document.getElementById('notifications').checked;
        }

        function updateRefreshInterval() {
            userSettings.refreshInterval = parseInt(document.getElementById('refreshInterval').value);
            // إعادة تعيين فترة التحديث
            if (typeof updateInterval !== 'undefined') {
                clearInterval(updateInterval);
                if (userSettings.refreshInterval > 0) {
                    updateInterval = setInterval(updateQuickStats, userSettings.refreshInterval * 1000);
                }
            }
        }

        // تحسين تجربة المستخدم - تأكيد قبل مغادرة الصفحة إذا كان هناك تغييرات غير محفوظة
        let hasUnsavedChanges = false;
        let autoSaveTimeout;

        function markAsChanged() {
            hasUnsavedChanges = true;

            // الحفظ التلقائي إذا كان مفعلاً
            if (userSettings.autoSave) {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(autoSave, 5000); // حفظ تلقائي بعد 5 ثوان
            }
        }

        function markAsSaved() {
            hasUnsavedChanges = false;
            clearTimeout(autoSaveTimeout);
        }

        function autoSave() {
            if (hasUnsavedChanges && userSettings.autoSave) {
                const form = document.querySelector('form');
                if (form) {
                    // محاولة الحفظ التلقائي
                    const formData = new FormData(form);
                    formData.append('auto_save', '1');

                    fetch(form.action || window.location.href, {
                        method: 'POST',
                        body: formData
                    }).then(response => {
                        if (response.ok) {
                            showGlobalMessage('تم الحفظ التلقائي', 'info', 2000);
                            markAsSaved();
                        }
                    }).catch(error => {
                        console.error('Auto-save failed:', error);
                    });
                }
            }
        }

        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges && !userSettings.autoSave) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟';
                return e.returnValue;
            }
        });

        // مراقبة التغييرات في النماذج مع تحسينات
        function initFormWatchers() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    // تجنب مراقبة حقول معينة
                    if (!input.classList.contains('no-watch')) {
                        input.addEventListener('change', markAsChanged);
                        input.addEventListener('input', debounce(markAsChanged, 500));
                    }
                });

                // إزالة العلامة عند الإرسال
                form.addEventListener('submit', markAsSaved);
            });
        }

        // وظيفة debounce لتحسين الأداء
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // وظيفة طباعة محسنة
        function printPage() {
            // إخفاء العناصر غير المطلوبة للطباعة
            const elementsToHide = document.querySelectorAll('.no-print');
            elementsToHide.forEach(el => el.style.display = 'none');

            window.print();

            // إعادة إظهار العناصر بعد الطباعة
            setTimeout(() => {
                elementsToHide.forEach(el => el.style.display = '');
            }, 1000);
        }

        // وظيفة تصدير البيانات محسنة
        function exportData(format, type, filters = {}) {
            showGlobalLoading('جاري تحضير البيانات للتصدير...');

            const params = new URLSearchParams({
                format: format,
                type: type,
                ...filters
            });

            const url = `export.php?${params.toString()}`;

            // فتح نافذة جديدة للتصدير
            const exportWindow = window.open(url, '_blank');

            // إخفاء مؤشر التحميل بعد فترة
            setTimeout(() => {
                hideGlobalLoading();
                if (exportWindow) {
                    showGlobalMessage('تم بدء عملية التصدير', 'success', 3000);
                } else {
                    showGlobalMessage('فشل في فتح نافذة التصدير. تحقق من إعدادات المتصفح.', 'warning', 5000);
                }
            }, 2000);
        }

        // وظيفة نسخ النص إلى الحافظة محسنة
        async function copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                showGlobalMessage('تم نسخ النص إلى الحافظة', 'success', 2000);
            } catch (err) {
                // طريقة بديلة للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showGlobalMessage('تم نسخ النص إلى الحافظة', 'success', 2000);
                } catch (fallbackErr) {
                    showGlobalMessage('فشل في نسخ النص', 'error', 2000);
                }
                document.body.removeChild(textArea);
            }
        }

        // تحديث الوقت الحالي في الفوتر
        function updateCurrentTime() {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = new Date().toLocaleTimeString('ar-EG');
            }
        }

        // تحسين إمكانية الوصول - دعم لوحة المفاتيح محسن
        document.addEventListener('keydown', function(e) {
            // Ctrl+S للحفظ
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const saveButton = document.querySelector('button[type="submit"], .btn-save');
                if (saveButton) {
                    saveButton.click();
                    showGlobalMessage('تم الحفظ بواسطة اختصار لوحة المفاتيح', 'info', 2000);
                }
            }

            // Ctrl+N لإضافة جديد
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                const newButton = document.querySelector('.btn-new, [href*="add_"]');
                if (newButton) {
                    newButton.click();
                }
            }

            // Ctrl+P للطباعة
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printPage();
            }

            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }

            // F1 للمساعدة
            if (e.key === 'F1') {
                e.preventDefault();
                toggleHelp();
            }
        });

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل الإعدادات
            loadSettings();

            // تهيئة مراقبة النماذج
            initFormWatchers();

            // تحديث الوقت كل ثانية
            setInterval(updateCurrentTime, 1000);

            // تحسين الأداء - تحميل الصور بشكل تدريجي
            if ('IntersectionObserver' in window) {
                const images = document.querySelectorAll('img[data-src]');
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            }

            // تتبع الصفحة
            console.log('WIDDX OMS - Page loaded:', window.location.pathname);
        });

        // تحسينات الأداء
        if ('requestIdleCallback' in window) {
            requestIdleCallback(function() {
                // تحميل المحتوى غير الضروري
                console.log('WIDDX OMS - Loading non-critical resources...');

                // يمكن إضافة تحميل ملفات إضافية هنا
                // مثل الخطوط الإضافية أو المكتبات غير الأساسية
            });
        }

        // معالجة الأخطاء العامة
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            // يمكن إرسال الأخطاء إلى خادم التسجيل
        });

        // معالجة الأخطاء في الـ Promise
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
            // يمكن إرسال الأخطاء إلى خادم التسجيل
        });
    </script>

</body>
</html>
