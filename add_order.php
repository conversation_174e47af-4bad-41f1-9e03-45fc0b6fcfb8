<?php
/**
 * صفحة إضافة طلبية جديدة - محدثة لاستخدام Layout الموحد
 */

// تحميل النظام
require_once 'config/database_fixed.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('إضافة طلبية جديدة')
    ->setPageDescription('إضافة طلبية جديدة للعملاء في النظام')
    ->setCurrentPage('add_order')
    ->addBreadcrumb('الرئيسية', 'index.php', 'fas fa-home')
    ->addBreadcrumb('إضافة طلبية جديدة', null, 'fas fa-plus-circle')
    ->showStatusBar(false);

$db = getDatabase();
$message = '';

// إنشاء مجلد الصور إذا لم يكن موجوداً
if (!file_exists('uploads')) {
    mkdir('uploads', 0777, true);
}

// معالجة إضافة عميل جديد من نفس الصفحة
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer_quick') {
    $name = trim($_POST['customer_name']);
    $phone = trim($_POST['customer_phone']);
    $email = trim($_POST['customer_email']);
    $address = trim($_POST['customer_address']);
    
    if (!empty($name)) {
        $db->query("INSERT INTO customers (name, phone, email, address) VALUES (:name, :phone, :email, :address)");
        $db->bind(':name', $name);
        $db->bind(':phone', $phone);
        $db->bind(':email', $email);
        $db->bind(':address', $address);
        
        if ($db->execute()) {
            $new_customer_id = $db->lastInsertId();
            $message = '<div class="alert alert-success">تم إضافة العميل بنجاح! يمكنك الآن إضافة الطلبية.</div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في إضافة العميل!</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
    }
}

// معالجة إضافة طلبية جديدة
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_order') {
    $customer_id = intval($_POST['customer_id']);
    $notes = trim($_POST['notes']);

    if ($customer_id > 0) {
        try {
            $db->beginTransaction();

            // التحقق من وجود العميل
            $db->query("SELECT id FROM customers WHERE id = :customer_id");
            $db->bind(':customer_id', $customer_id);
            $customer_check = $db->single();

            if (!$customer_check) {
                throw new Exception("العميل المحدد غير موجود");
            }

            // إنشاء الطلبية
            $db->query("INSERT INTO orders (customer_id, notes, status, total_amount) VALUES (:customer_id, :notes, :status, :total_amount)");
            $db->bind(':customer_id', $customer_id);
            $db->bind(':notes', $notes);
            $db->bind(':status', 'pending');
            $db->bind(':total_amount', 0);

            if (!$db->execute()) {
                throw new Exception("فشل في إنشاء الطلبية");
            }

            $order_id = $db->lastInsertId();

            if (!$order_id) {
                throw new Exception("لم يتم الحصول على رقم الطلبية");
            }
            
            // إضافة عناصر الطلبية
            if (isset($_POST['product_names']) && is_array($_POST['product_names'])) {
                foreach ($_POST['product_names'] as $index => $product_name) {
                    if (!empty($product_name) && !empty($_POST['quantities'][$index])) {
                        $product_name = trim($product_name);
                        $quantity = intval($_POST['quantities'][$index]);
                        $width = !empty($_POST['widths'][$index]) ? floatval($_POST['widths'][$index]) : null;
                        $height = !empty($_POST['heights'][$index]) ? floatval($_POST['heights'][$index]) : null;
                        $depth = !empty($_POST['depths'][$index]) ? floatval($_POST['depths'][$index]) : null;
                        $item_notes = trim($_POST['item_notes'][$index]);
                        $unit_price = !empty($_POST['unit_prices'][$index]) ? floatval($_POST['unit_prices'][$index]) : 0;
                        $total_price = $unit_price * $quantity;
                        
                        // معالجة رفع الصورة
                        $image_path = '';
                        if (isset($_FILES['product_images']) && isset($_FILES['product_images']['name'][$index]) && $_FILES['product_images']['error'][$index] == 0) {
                            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                            $file_type = $_FILES['product_images']['type'][$index];
                            
                            if (in_array($file_type, $allowed_types)) {
                                $file_extension = pathinfo($_FILES['product_images']['name'][$index], PATHINFO_EXTENSION);
                                $new_filename = uniqid() . '.' . $file_extension;
                                $upload_path = 'uploads/' . $new_filename;
                                
                                if (move_uploaded_file($_FILES['product_images']['tmp_name'][$index], $upload_path)) {
                                    $image_path = $upload_path;
                                }
                            }
                        }
                        
                        $db->query("INSERT INTO order_items (order_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes)
                                   VALUES (:order_id, :product_name, :product_image, :quantity, :width, :height, :depth, :unit_price, :total_price, :notes)");
                        $db->bind(':order_id', $order_id);
                        $db->bind(':product_name', $product_name);
                        $db->bind(':product_image', $image_path);
                        $db->bind(':quantity', $quantity);
                        $db->bind(':width', $width);
                        $db->bind(':height', $height);
                        $db->bind(':depth', $depth);
                        $db->bind(':unit_price', $unit_price);
                        $db->bind(':total_price', $total_price);
                        $db->bind(':notes', $item_notes);

                        if (!$db->execute()) {
                            throw new Exception("فشل في إضافة المنتج: " . $product_name);
                        }
                    }
                }
            }
            
            // تحديث إجمالي الطلبية
            $db->query("UPDATE orders SET total_amount = (SELECT SUM(total_price) FROM order_items WHERE order_id = :order_id) WHERE id = :order_id");
            $db->bind(':order_id', $order_id);
            $db->execute();
            
            $db->commit();
            $message = '<div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم إنشاء الطلبية بنجاح! رقم الطلبية: ' . $order_id . '
                <div class="mt-2">
                    <a href="view_order.php?id=' . $order_id . '" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i> عرض الطلبية
                    </a>
                    <a href="add_order.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> إضافة طلبية أخرى
                    </a>
                </div>
            </div>';
            
        } catch (Exception $e) {
            $db->rollBack();
            $message = '<div class="alert alert-danger">حدث خطأ في إنشاء الطلبية: ' . $e->getMessage() . '</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى اختيار العميل!</div>';
    }
}

// جلب العملاء للقائمة المنسدلة
$db->query("SELECT id, name, phone FROM customers ORDER BY name");
$customers = $db->resultset();

// تحديد العميل المحدد مسبقاً
$selected_customer = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : (isset($new_customer_id) ? $new_customer_id : 0);

// إضافة CSS مخصص للصفحة
$layout->addCustomStyles('
    .product-row {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    .product-row:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    .customer-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #bbdefb;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-outline-primary:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }
');

// بدء Layout
$layout->startLayout();

// إظهار رسالة إذا وجدت
if (!empty($message)) {
    echo $message;
}
?>

<div class="mt-4">

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-plus-circle"></i> إضافة طلبية جديدة</h4>
                    </div>
                    <div class="card-body">
                        <!-- قسم اختيار العميل -->
                        <div class="customer-section">
                            <h5><i class="fas fa-user"></i> بيانات العميل</h5>

                            <!-- اختيار عميل موجود -->
                            <div class="mb-3">
                                <label class="form-label">اختيار عميل موجود</label>
                                <select class="form-select" id="existing_customer" onchange="selectExistingCustomer()">
                                    <option value="">-- اختر عميل موجود --</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>"
                                                <?php echo ($selected_customer == $customer['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if (!empty($customer['phone'])): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="text-center mb-3">
                                <strong>أو</strong>
                            </div>

                            <!-- إضافة عميل جديد -->
                            <div class="border rounded p-3" style="background-color: white;">
                                <h6><i class="fas fa-user-plus"></i> إضافة عميل جديد</h6>
                                <form method="POST" id="newCustomerForm">
                                    <input type="hidden" name="action" value="add_customer_quick">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" name="customer_name" placeholder="اسم العميل *">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="tel" class="form-control" name="customer_phone" placeholder="رقم الهاتف">
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <input type="email" class="form-control" name="customer_email" placeholder="البريد الإلكتروني">
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <input type="text" class="form-control" name="customer_address" placeholder="العنوان">
                                        </div>
                                        <div class="col-12 mt-2">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-save"></i> حفظ العميل الجديد
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- نموذج الطلبية -->
                        <form method="POST" enctype="multipart/form-data" id="orderForm">
                            <input type="hidden" name="action" value="add_order">
                            <input type="hidden" name="customer_id" id="selected_customer_id" value="<?php echo $selected_customer; ?>">

                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-box"></i> المنتجات المطلوب تصنيعها</label>
                                <div id="products-container">
                                    <div class="product-row">
                                        <div class="row g-2">
                                            <div class="col-md-6">
                                                <label class="form-label">اسم المنتج *</label>
                                                <input type="text" class="form-control" name="product_names[]" placeholder="مثال: خزانة ملابس مخصصة" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">صورة المنتج</label>
                                                <input type="file" class="form-control" name="product_images[]" accept="image/*">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">الكمية *</label>
                                                <input type="number" class="form-control" name="quantities[]" placeholder="1" min="1" required>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">العرض (سم)</label>
                                                <input type="number" class="form-control" name="widths[]" placeholder="200" step="0.01">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">الارتفاع (سم)</label>
                                                <input type="number" class="form-control" name="heights[]" placeholder="220" step="0.01">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">العمق (سم)</label>
                                                <input type="number" class="form-control" name="depths[]" placeholder="60" step="0.01">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">سعر الوحدة (جنيه)</label>
                                                <input type="number" class="form-control" name="unit_prices[]" placeholder="2500" step="0.01" min="0">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">ملاحظات المنتج</label>
                                                <input type="text" class="form-control" name="item_notes[]" placeholder="مثال: خشب زان مع أدراج إضافية">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary" onclick="addProductRow()">
                                    <i class="fas fa-plus"></i> إضافة منتج آخر
                                </button>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label"><i class="fas fa-sticky-note"></i> ملاحظات الطلبية</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أي ملاحظات إضافية حول الطلبية..."></textarea>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitOrderBtn" disabled>
                                    <i class="fas fa-save"></i> إنشاء الطلبية
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
        // تحديث حالة زر الإرسال
        function updateSubmitButton() {
            const customerId = document.getElementById("selected_customer_id").value;
            const submitBtn = document.getElementById("submitOrderBtn");

            if (customerId && customerId !== "0") {
                submitBtn.disabled = false;
                submitBtn.innerHTML = "<i class=\"fas fa-save\"></i> إنشاء الطلبية";
            } else {
                submitBtn.disabled = true;
                submitBtn.innerHTML = "<i class=\"fas fa-exclamation-triangle\"></i> يرجى اختيار العميل أولاً";
            }
        }

        // اختيار عميل موجود
        function selectExistingCustomer() {
            const select = document.getElementById("existing_customer");
            const customerIdInput = document.getElementById("selected_customer_id");

            customerIdInput.value = select.value;
            updateSubmitButton();

            // إخفاء نموذج العميل الجديد إذا تم اختيار عميل موجود
            const newCustomerForm = document.getElementById("newCustomerForm");
            if (select.value) {
                newCustomerForm.style.opacity = "0.5";
                newCustomerForm.style.pointerEvents = "none";
            } else {
                newCustomerForm.style.opacity = "1";
                newCustomerForm.style.pointerEvents = "auto";
            }
        }

        // إضافة صف منتج جديد
        function addProductRow() {
            const container = document.getElementById("products-container");
            const productRow = document.createElement("div");
            productRow.className = "product-row";
            // إنشاء HTML بطريقة أبسط
            productRow.innerHTML = "<div class=\"row g-2\"><div class=\"col-md-6\"><label class=\"form-label\">اسم المنتج *</label><input type=\"text\" class=\"form-control\" name=\"product_names[]\" placeholder=\"مثال: طاولة مكتب مخصصة\" required></div><div class=\"col-md-6\"><label class=\"form-label\">صورة المنتج</label><input type=\"file\" class=\"form-control\" name=\"product_images[]\" accept=\"image/*\"></div><div class=\"col-md-3\"><label class=\"form-label\">الكمية *</label><input type=\"number\" class=\"form-control\" name=\"quantities[]\" placeholder=\"1\" min=\"1\" required></div><div class=\"col-md-3\"><label class=\"form-label\">العرض (سم)</label><input type=\"number\" class=\"form-control\" name=\"widths[]\" placeholder=\"150\" step=\"0.01\"></div><div class=\"col-md-3\"><label class=\"form-label\">الارتفاع (سم)</label><input type=\"number\" class=\"form-control\" name=\"heights[]\" placeholder=\"75\" step=\"0.01\"></div><div class=\"col-md-3\"><label class=\"form-label\">العمق (سم)</label><input type=\"number\" class=\"form-control\" name=\"depths[]\" placeholder=\"70\" step=\"0.01\"></div><div class=\"col-md-5\"><label class=\"form-label\">سعر الوحدة (جنيه)</label><input type=\"number\" class=\"form-control\" name=\"unit_prices[]\" placeholder=\"1200\" step=\"0.01\" min=\"0\"></div><div class=\"col-md-5\"><label class=\"form-label\">ملاحظات المنتج</label><input type=\"text\" class=\"form-control\" name=\"item_notes[]\" placeholder=\"مثال: مع أدراج جانبية\"></div><div class=\"col-md-2\"><label class=\"form-label\">&nbsp;</label><button type=\"button\" class=\"btn btn-outline-danger w-100\" onclick=\"removeProductRow(this)\"><i class=\"fas fa-trash\"></i></button></div></div>";
            container.appendChild(productRow);
        }

        // حذف صف منتج
        function removeProductRow(button) {
            button.closest(".product-row").remove();
        }

        // تحديث حالة الزر عند تحميل الصفحة
        document.addEventListener("DOMContentLoaded", function() {
            updateSubmitButton();
            selectExistingCustomer(); // لتطبيق التأثيرات الأولية
        });
');

// إنهاء Layout
$layout->endLayout();
?>
