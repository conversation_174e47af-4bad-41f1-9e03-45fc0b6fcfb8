<?php
/**
 * ملف Layout الموحد لنظام WIDDX OMS
 * يوفر نظام موحد لجميع صفحات المشروع مع إمكانيات متقدمة
 */

// منع الوصول المباشر
if (!defined('WIDDX_OMS')) {
    define('WIDDX_OMS', true);
}

class WIDDXLayout {
    private $pageTitle = 'نظام إدارة الطلبيات';
    private $pageDescription = 'نظام إدارة الطلبيات WIDDX OMS - نظام شامل لإدارة العملاء والطلبيات';
    private $currentPage = '';
    private $breadcrumbs = [];
    private $showBreadcrumb = false;
    private $showStatusBar = false;
    private $additionalCSS = [];
    private $additionalJS = [];
    private $customStyles = '';
    private $inlineJS = '';
    private $pageMetaTags = [];
    private $bodyClass = '';
    private $containerClass = 'container';
    private $contentClass = '';
    
    /**
     * إعداد عنوان الصفحة
     */
    public function setPageTitle($title) {
        $this->pageTitle = $title;
        return $this;
    }
    
    /**
     * إعداد وصف الصفحة
     */
    public function setPageDescription($description) {
        $this->pageDescription = $description;
        return $this;
    }
    
    /**
     * إعداد الصفحة الحالية (لتفعيل الرابط في القائمة)
     */
    public function setCurrentPage($page) {
        $this->currentPage = $page;
        return $this;
    }
    
    /**
     * إضافة مسار تنقل
     */
    public function addBreadcrumb($title, $url = null, $icon = null) {
        $this->breadcrumbs[] = [
            'title' => $title,
            'url' => $url,
            'icon' => $icon
        ];
        $this->showBreadcrumb = true;
        return $this;
    }
    
    /**
     * إظهار شريط الحالة
     */
    public function showStatusBar($show = true) {
        $this->showStatusBar = $show;
        return $this;
    }
    
    /**
     * إضافة ملف CSS إضافي
     */
    public function addCSS($cssFile) {
        $this->additionalCSS[] = $cssFile;
        return $this;
    }
    
    /**
     * إضافة ملف JavaScript إضافي
     */
    public function addJS($jsFile) {
        $this->additionalJS[] = $jsFile;
        return $this;
    }
    
    /**
     * إضافة CSS مخصص
     */
    public function addCustomStyles($styles) {
        $this->customStyles .= $styles;
        return $this;
    }
    
    /**
     * إضافة JavaScript مضمن
     */
    public function addInlineJS($js) {
        $this->inlineJS .= $js;
        return $this;
    }
    
    /**
     * إضافة meta tag مخصص
     */
    public function addMetaTag($name, $content) {
        $this->pageMetaTags[$name] = $content;
        return $this;
    }
    
    /**
     * إعداد class للـ body
     */
    public function setBodyClass($class) {
        $this->bodyClass = $class;
        return $this;
    }
    
    /**
     * إعداد class للحاوي الرئيسي
     */
    public function setContainerClass($class) {
        $this->containerClass = $class;
        return $this;
    }
    
    /**
     * إعداد class للمحتوى
     */
    public function setContentClass($class) {
        $this->contentClass = $class;
        return $this;
    }
    
    /**
     * بدء الـ Layout وإظهار الـ Header
     */
    public function startLayout() {
        // تمرير المتغيرات للـ header
        $pageTitle = $this->pageTitle;
        $pageDescription = $this->pageDescription;
        $currentPage = $this->currentPage;
        $breadcrumbs = $this->breadcrumbs;
        $showBreadcrumb = $this->showBreadcrumb;
        $showStatusBar = $this->showStatusBar;
        $additionalCSS = $this->additionalCSS;
        $customStyles = $this->customStyles;
        $pageMetaTags = $this->pageMetaTags;
        
        // إضافة class للـ body إذا كان محدد
        if (!empty($this->bodyClass)) {
            $customStyles .= "body { " . $this->bodyClass . " }";
        }
        
        // تضمين الـ header
        include __DIR__ . '/header.php';
        
        // بدء المحتوى الرئيسي
        echo '<div class="' . $this->containerClass . '">';
        if (!empty($this->contentClass)) {
            echo '<div class="' . $this->contentClass . '">';
        }
    }
    
    /**
     * إنهاء الـ Layout وإظهار الـ Footer
     */
    public function endLayout() {
        // إنهاء المحتوى الرئيسي
        if (!empty($this->contentClass)) {
            echo '</div>';
        }
        echo '</div>';
        
        // تمرير المتغيرات للـ footer
        $additionalJS = $this->additionalJS;
        $inlineJS = $this->inlineJS;
        
        // تضمين الـ footer
        include __DIR__ . '/footer.php';
    }
    
    /**
     * إظهار رسالة تنبيه
     */
    public static function showAlert($message, $type = 'info', $dismissible = true) {
        $alertClass = [
            'success' => 'alert-success',
            'error' => 'alert-danger',
            'warning' => 'alert-warning',
            'info' => 'alert-info'
        ][$type] ?? 'alert-info';
        
        $icon = [
            'success' => 'fas fa-check-circle',
            'error' => 'fas fa-exclamation-triangle',
            'warning' => 'fas fa-exclamation-circle',
            'info' => 'fas fa-info-circle'
        ][$type] ?? 'fas fa-info-circle';
        
        $dismissibleClass = $dismissible ? 'alert-dismissible' : '';
        $dismissButton = $dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>' : '';
        
        echo '<div class="alert ' . $alertClass . ' ' . $dismissibleClass . ' fade show shadow-sm" role="alert">';
        echo '<i class="' . $icon . ' me-2"></i>' . htmlspecialchars($message);
        echo $dismissButton;
        echo '</div>';
    }
    
    /**
     * إظهار بطاقة محتوى
     */
    public static function showCard($title, $content, $headerClass = '', $bodyClass = '', $icon = '') {
        echo '<div class="card shadow-sm">';
        if (!empty($title)) {
            echo '<div class="card-header ' . $headerClass . '">';
            if (!empty($icon)) {
                echo '<i class="' . $icon . ' me-2"></i>';
            }
            echo '<h5 class="mb-0">' . htmlspecialchars($title) . '</h5>';
            echo '</div>';
        }
        echo '<div class="card-body ' . $bodyClass . '">';
        echo $content;
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * إظهار جدول بيانات
     */
    public static function showDataTable($headers, $data, $tableClass = 'table table-striped table-hover') {
        echo '<div class="table-responsive">';
        echo '<table class="' . $tableClass . '">';
        
        // رؤوس الجدول
        if (!empty($headers)) {
            echo '<thead class="table-dark">';
            echo '<tr>';
            foreach ($headers as $header) {
                echo '<th>' . htmlspecialchars($header) . '</th>';
            }
            echo '</tr>';
            echo '</thead>';
        }
        
        // بيانات الجدول
        echo '<tbody>';
        if (!empty($data)) {
            foreach ($data as $row) {
                echo '<tr>';
                foreach ($row as $cell) {
                    echo '<td>' . $cell . '</td>';
                }
                echo '</tr>';
            }
        } else {
            $colspan = count($headers);
            echo '<tr>';
            echo '<td colspan="' . $colspan . '" class="text-center text-muted py-4">';
            echo '<i class="fas fa-inbox fa-2x mb-2"></i><br>لا توجد بيانات للعرض';
            echo '</td>';
            echo '</tr>';
        }
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }
    
    /**
     * إظهار نموذج بحث
     */
    public static function showSearchForm($action = '', $fields = [], $method = 'GET') {
        echo '<div class="card mb-4">';
        echo '<div class="card-header">';
        echo '<h6 class="mb-0"><i class="fas fa-search me-2"></i>البحث والفلترة</h6>';
        echo '</div>';
        echo '<div class="card-body">';
        echo '<form method="' . $method . '" action="' . $action . '" class="row g-3">';
        
        foreach ($fields as $field) {
            echo '<div class="col-md-' . ($field['width'] ?? '4') . '">';
            echo '<label for="' . $field['name'] . '" class="form-label">' . $field['label'] . '</label>';
            
            if ($field['type'] === 'select') {
                echo '<select class="form-select" name="' . $field['name'] . '" id="' . $field['name'] . '">';
                foreach ($field['options'] as $value => $text) {
                    $selected = (isset($field['selected']) && $field['selected'] == $value) ? 'selected' : '';
                    echo '<option value="' . $value . '" ' . $selected . '>' . $text . '</option>';
                }
                echo '</select>';
            } else {
                $value = $field['value'] ?? '';
                echo '<input type="' . $field['type'] . '" class="form-control" name="' . $field['name'] . '" id="' . $field['name'] . '" value="' . $value . '" placeholder="' . ($field['placeholder'] ?? '') . '">';
            }
            
            echo '</div>';
        }
        
        echo '<div class="col-md-12">';
        echo '<button type="submit" class="btn btn-primary me-2"><i class="fas fa-search me-1"></i>بحث</button>';
        echo '<a href="' . $action . '" class="btn btn-outline-secondary"><i class="fas fa-times me-1"></i>إلغاء</a>';
        echo '</div>';
        echo '</form>';
        echo '</div>';
        echo '</div>';
    }
}

/**
 * وظائف مساعدة سريعة
 */

// إنشاء مثيل Layout جديد
function createLayout() {
    return new WIDDXLayout();
}

// إظهار رسالة سريعة
function showMessage($message, $type = 'info') {
    WIDDXLayout::showAlert($message, $type);
}

// إظهار بطاقة سريعة
function showCard($title, $content, $icon = '') {
    WIDDXLayout::showCard($title, $content, '', '', $icon);
}
?>
