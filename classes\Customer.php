<?php
/**
 * كلاس العملاء لنظام WIDDX OMS
 * يحتوي على جميع العمليات المتعلقة بإدارة العملاء
 */

require_once 'Database.php';

class Customer {
    private $db;
    private $id;
    private $name;
    private $phone;
    private $email;
    private $address;
    private $created_at;
    private $updated_at;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    // إضافة عميل جديد
    public function create($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateData($data);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message']
                ];
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($data['email']) && $this->emailExists($data['email'])) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني مستخدم مسبقاً'
                ];
            }
            
            // إدراج العميل الجديد
            $query = "INSERT INTO customers (name, phone, email, address) 
                     VALUES (:name, :phone, :email, :address)";
            
            $this->db->query($query);
            $this->db->bind(':name', trim($data['name']));
            $this->db->bind(':phone', trim($data['phone']));
            $this->db->bind(':email', trim($data['email']));
            $this->db->bind(':address', trim($data['address']));
            
            if ($this->db->execute()) {
                $customerId = $this->db->lastInsertId();
                
                return [
                    'success' => true,
                    'message' => 'تم إضافة العميل بنجاح',
                    'customer_id' => $customerId
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في إضافة العميل'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // تحديث بيانات العميل
    public function update($id, $data) {
        try {
            // التحقق من وجود العميل
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }
            
            // التحقق من صحة البيانات
            $validation = $this->validateData($data);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message']
                ];
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني (باستثناء العميل الحالي)
            if (!empty($data['email']) && $this->emailExists($data['email'], $id)) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني مستخدم مسبقاً'
                ];
            }
            
            // تحديث بيانات العميل
            $query = "UPDATE customers 
                     SET name = :name, phone = :phone, email = :email, address = :address,
                         updated_at = CURRENT_TIMESTAMP
                     WHERE id = :id";
            
            $this->db->query($query);
            $this->db->bind(':id', $id);
            $this->db->bind(':name', trim($data['name']));
            $this->db->bind(':phone', trim($data['phone']));
            $this->db->bind(':email', trim($data['email']));
            $this->db->bind(':address', trim($data['address']));
            
            if ($this->db->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في تحديث البيانات'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // حذف عميل
    public function delete($id) {
        try {
            // التحقق من وجود العميل
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }
            
            // التحقق من عدم وجود طلبيات للعميل
            if ($this->hasOrders($id)) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن حذف العميل لأن لديه طلبيات مسجلة'
                ];
            }
            
            // حذف العميل
            $query = "DELETE FROM customers WHERE id = :id";
            $this->db->query($query);
            $this->db->bind(':id', $id);
            
            if ($this->db->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم حذف العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في حذف العميل'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // الحصول على بيانات عميل
    public function getById($id) {
        try {
            $query = "SELECT * FROM customers WHERE id = :id";
            $this->db->query($query);
            $this->db->bind(':id', $id);
            
            return $this->db->single();
        } catch (Exception $e) {
            return null;
        }
    }
    
    // الحصول على جميع العملاء مع إحصائياتهم
    public function getAllWithStats() {
        try {
            $query = "SELECT c.*,
                     COUNT(o.id) as orders_count,
                     SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                     SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_count,
                     SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                     SUM(CASE WHEN o.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count,
                     MAX(o.created_at) as last_order_date,
                     SUM(o.total_amount) as total_spent
                     FROM customers c
                     LEFT JOIN orders o ON c.id = o.customer_id
                     GROUP BY c.id
                     ORDER BY c.name";
            
            $this->db->query($query);
            return $this->db->resultset();
        } catch (Exception $e) {
            return [];
        }
    }
    
    // البحث في العملاء
    public function search($searchTerm) {
        try {
            $query = "SELECT c.*,
                     COUNT(o.id) as orders_count
                     FROM customers c
                     LEFT JOIN orders o ON c.id = o.customer_id
                     WHERE c.name LIKE :search 
                        OR c.phone LIKE :search 
                        OR c.email LIKE :search
                     GROUP BY c.id
                     ORDER BY c.name";
            
            $this->db->query($query);
            $this->db->bind(':search', '%' . $searchTerm . '%');
            
            return $this->db->resultset();
        } catch (Exception $e) {
            return [];
        }
    }
    
    // الحصول على العملاء النشطين (لديهم طلبيات معلقة أو قيد التنفيذ)
    public function getActiveCustomers() {
        try {
            $query = "SELECT c.*,
                     COUNT(o.id) as orders_count,
                     SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                     SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_count,
                     SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                     SUM(CASE WHEN o.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count
                     FROM customers c
                     INNER JOIN orders o ON c.id = o.customer_id
                     WHERE o.status IN ('pending', 'processing')
                     GROUP BY c.id
                     HAVING pending_count > 0 OR processing_count > 0
                     ORDER BY pending_count DESC, processing_count DESC, c.name";
            
            $this->db->query($query);
            return $this->db->resultset();
        } catch (Exception $e) {
            return [];
        }
    }
    
    // التحقق من صحة البيانات
    private function validateData($data) {
        $errors = [];
        
        // التحقق من الاسم
        if (empty(trim($data['name']))) {
            $errors[] = 'اسم العميل مطلوب';
        } elseif (strlen(trim($data['name'])) < 2) {
            $errors[] = 'اسم العميل يجب أن يكون أكثر من حرفين';
        }
        
        // التحقق من رقم الهاتف
        if (!empty($data['phone'])) {
            $phone = preg_replace('/[^0-9]/', '', $data['phone']);
            if (strlen($phone) < 10 || strlen($phone) > 15) {
                $errors[] = 'رقم الهاتف غير صحيح';
            }
        }
        
        // التحقق من البريد الإلكتروني
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        return [
            'valid' => empty($errors),
            'message' => implode(', ', $errors)
        ];
    }
    
    // التحقق من وجود العميل
    private function exists($id) {
        $query = "SELECT id FROM customers WHERE id = :id";
        $this->db->query($query);
        $this->db->bind(':id', $id);
        $result = $this->db->single();
        
        return !empty($result);
    }
    
    // التحقق من تكرار البريد الإلكتروني
    private function emailExists($email, $excludeId = null) {
        $query = "SELECT id FROM customers WHERE email = :email";
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
        }
        
        $this->db->query($query);
        $this->db->bind(':email', $email);
        
        if ($excludeId) {
            $this->db->bind(':exclude_id', $excludeId);
        }
        
        $result = $this->db->single();
        return !empty($result);
    }
    
    // التحقق من وجود طلبيات للعميل
    private function hasOrders($customerId) {
        $query = "SELECT COUNT(*) as count FROM orders WHERE customer_id = :customer_id";
        $this->db->query($query);
        $this->db->bind(':customer_id', $customerId);
        $result = $this->db->single();
        
        return $result['count'] > 0;
    }
    
    // الحصول على إحصائيات العملاء
    public function getStats() {
        try {
            $stats = [];
            
            // إجمالي العملاء
            $query = "SELECT COUNT(*) as total FROM customers";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['total_customers'] = $result['total'];
            
            // العملاء النشطين
            $query = "SELECT COUNT(DISTINCT customer_id) as active 
                     FROM orders 
                     WHERE status IN ('pending', 'processing')";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['active_customers'] = $result['active'];
            
            // العملاء الجدد هذا الشهر
            $query = "SELECT COUNT(*) as new_this_month 
                     FROM customers 
                     WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
                       AND YEAR(created_at) = YEAR(CURRENT_DATE())";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['new_this_month'] = $result['new_this_month'];
            
            return $stats;
        } catch (Exception $e) {
            return [];
        }
    }
}
