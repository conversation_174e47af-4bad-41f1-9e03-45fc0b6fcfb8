/**
 * ملف JavaScript الرئيسي المحسن لنظام WIDDX OMS
 * يحتوي على جميع الوظائف التفاعلية والميزات المتقدمة للنظام
 */

// كلاس رئيسي محسن لإدارة النظام
class WIDDXSystem {
    constructor() {
        this.loadedCustomers = new Set();
        this.cache = new Map();
        this.eventListeners = new Map();
        this.config = {
            animationDuration: 300,
            debounceDelay: 500,
            cacheExpiry: 300000, // 5 دقائق
            maxRetries: 3
        };
        this.init();
    }

    // تهيئة النظام المحسنة
    init() {
        this.bindEvents();
        this.initAnimations();
        this.initTooltips();
        this.initServiceWorker();
        this.initPerformanceMonitoring();
        this.setupErrorHandling();
    }

    // ربط الأحداث المحسن
    bindEvents() {
        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', () => {
            this.animateCards();
            this.initLazyLoading();
            this.setupFormValidation();
        });

        // تأكيد الحذف المحسن
        this.delegateEvent(document, 'click', '[data-confirm]', (e, element) => {
            const message = element.getAttribute('data-confirm');
            const title = element.getAttribute('data-confirm-title') || 'تأكيد العملية';

            if (!this.showConfirmDialog(title, message)) {
                e.preventDefault();
            }
        });

        // مراقبة تغييرات الاتصال
        window.addEventListener('online', () => this.handleConnectionChange(true));
        window.addEventListener('offline', () => this.handleConnectionChange(false));

        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, this.config.debounceDelay));
    }

    // وظيفة debounce محسنة
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تفويض الأحداث لتحسين الأداء
    delegateEvent(parent, eventType, selector, handler) {
        const wrappedHandler = (e) => {
            const target = e.target.closest(selector);
            if (target) {
                handler(e, target);
            }
        };

        parent.addEventListener(eventType, wrappedHandler);

        // حفظ مرجع للإزالة لاحقاً
        const key = `${eventType}-${selector}`;
        this.eventListeners.set(key, { parent, eventType, handler: wrappedHandler });
    }

    // إعداد Service Worker
    initServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    }

    // مراقبة الأداء
    initPerformanceMonitoring() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'navigation') {
                        console.log('Page load time:', entry.loadEventEnd - entry.loadEventStart);
                    }
                }
            });
            observer.observe({ entryTypes: ['navigation'] });
        }
    }

    // إعداد معالجة الأخطاء
    setupErrorHandling() {
        window.addEventListener('error', (e) => {
            this.logError('JavaScript Error', e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
            this.logError('Unhandled Promise Rejection', e.reason);
        });
    }

    // تحميل طلبيات العميل المحسن
    async loadCustomerOrders(customerId, forceReload = false) {
        // التحقق من صحة المعرف
        if (!customerId || isNaN(customerId)) {
            this.showAlert('معرف العميل غير صحيح', 'error');
            return;
        }

        // التحقق من الكاش
        const cacheKey = `customer-orders-${customerId}`;
        if (!forceReload && this.loadedCustomers.has(customerId) && this.cache.has(cacheKey)) {
            const cachedData = this.cache.get(cacheKey);
            if (Date.now() - cachedData.timestamp < this.config.cacheExpiry) {
                return;
            }
        }

        const container = document.getElementById(`orders-container-${customerId}`);
        if (!container) {
            console.warn(`Container not found for customer ${customerId}`);
            return;
        }

        try {
            // إظهار مؤشر التحميل المحسن
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="text-muted mb-0">جاري تحميل طلبيات العميل...</p>
                </div>
            `;

            // تحميل البيانات مع إعادة المحاولة
            const data = await this.fetchWithRetry(`get_customer_orders.php?customer_id=${customerId}`);

            // تحديث المحتوى
            container.innerHTML = data;

            // حفظ في الكاش
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            this.loadedCustomers.add(customerId);
            this.animateOrderRows();

            // تسجيل النشاط
            this.logActivity('customer_orders_loaded', { customerId });

        } catch (error) {
            console.error('Error loading customer orders:', error);
            this.handleLoadError(container, error, () => this.loadCustomerOrders(customerId, true));
        }
    }

    // وظيفة fetch مع إعادة المحاولة
    async fetchWithRetry(url, options = {}, retries = this.config.maxRetries) {
        for (let i = 0; i <= retries; i++) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        ...options.headers
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.text();
            } catch (error) {
                if (i === retries) {
                    throw error;
                }

                // انتظار متزايد بين المحاولات
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
            }
        }
    }

    // معالجة أخطاء التحميل
    handleLoadError(container, error, retryCallback) {
        const errorMessage = this.getErrorMessage(error);
        container.innerHTML = `
            <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="fas fa-exclamation-triangle me-3"></i>
                <div class="flex-grow-1">
                    <strong>حدث خطأ في التحميل</strong><br>
                    <small>${errorMessage}</small>
                </div>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="(${retryCallback.toString()})()">
                    <i class="fas fa-redo me-1"></i>إعادة المحاولة
                </button>
            </div>
        `;
    }

    // الحصول على رسالة خطأ مفهومة
    getErrorMessage(error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return 'مشكلة في الاتصال بالخادم';
        } else if (error.message.includes('404')) {
            return 'الصفحة المطلوبة غير موجودة';
        } else if (error.message.includes('500')) {
            return 'خطأ في الخادم';
        } else {
            return 'خطأ غير متوقع';
        }
    }

    // تحديث حالة الطلبية المحسن
    async updateOrderStatus(orderId, status, element = null) {
        // التحقق من صحة البيانات
        if (!orderId || !status) {
            this.showAlert('بيانات غير صحيحة', 'error');
            return false;
        }

        // التحقق من الحالات المسموحة
        const allowedStatuses = ['pending', 'processing', 'completed', 'cancelled'];
        if (!allowedStatuses.includes(status)) {
            this.showAlert('حالة غير صحيحة', 'error');
            return false;
        }

        try {
            // إظهار مؤشر التحميل
            if (element) {
                this.showLoadingButton(element);
            } else {
                showGlobalLoading('جاري تحديث حالة الطلبية...');
            }

            const formData = new FormData();
            formData.append('order_id', orderId);
            formData.append('status', status);
            formData.append('update_status', '1');
            formData.append('timestamp', Date.now());

            const response = await fetch('update_order_status.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // إظهار رسالة نجاح
                this.showAlert(data.message || 'تم تحديث حالة الطلبية بنجاح', 'success');

                // تحديث الواجهة محلياً
                this.updateOrderStatusUI(orderId, status);

                // تسجيل النشاط
                this.logActivity('order_status_updated', { orderId, status });

                // إعادة تحميل الإحصائيات
                if (typeof updateQuickStats === 'function') {
                    updateQuickStats();
                }

                return true;
            } else {
                throw new Error(data.message || 'حدث خطأ في تحديث الحالة');
            }

        } catch (error) {
            console.error('Error updating order status:', error);
            this.showAlert(this.getErrorMessage(error), 'error');
            return false;
        } finally {
            // إخفاء مؤشر التحميل
            if (element) {
                this.hideLoadingButton(element);
            } else {
                hideGlobalLoading();
            }
        }
    }

    // تحديث واجهة المستخدم محلياً
    updateOrderStatusUI(orderId, status) {
        const statusElements = document.querySelectorAll(`[data-order-id="${orderId}"] .status-badge`);
        const statusTexts = {
            'pending': { text: 'معلقة', class: 'bg-warning text-dark' },
            'processing': { text: 'قيد التنفيذ', class: 'bg-info text-white' },
            'completed': { text: 'مكتملة', class: 'bg-success text-white' },
            'cancelled': { text: 'ملغية', class: 'bg-danger text-white' }
        };

        statusElements.forEach(element => {
            const statusInfo = statusTexts[status];
            if (statusInfo) {
                element.className = `badge status-badge ${statusInfo.class}`;
                element.textContent = statusInfo.text;

                // تأثير بصري
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        });

        // تحديث قوائم الاختيار
        const selectElements = document.querySelectorAll(`[data-order-id="${orderId}"] select[name="status"]`);
        selectElements.forEach(select => {
            select.value = status;
        });
    }

    // عرض رسالة تنبيه محسنة
    showAlert(message, type = 'info', duration = 5000, actions = []) {
        // استخدام الوظيفة العامة إذا كانت متاحة
        if (typeof showGlobalMessage === 'function') {
            showGlobalMessage(message, type, duration);
            return;
        }

        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const icon = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-triangle',
            'warning': 'fas fa-exclamation-circle',
            'info': 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        const alertId = 'alert-' + Date.now();

        // إنشاء أزرار الإجراءات
        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = '<div class="mt-2">';
            actions.forEach(action => {
                actionsHtml += `<button type="button" class="btn btn-sm btn-outline-${type} me-2" onclick="${action.callback}">${action.text}</button>`;
            });
            actionsHtml += '</div>';
        }

        const alertHtml = `
            <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show shadow-sm" role="alert">
                <div class="d-flex align-items-start">
                    <i class="${icon} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        ${message}
                        ${actionsHtml}
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        `;

        // إدراج الرسالة
        const container = document.querySelector('.container') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // تأثير صوتي
        this.playNotificationSound(type);

        // إزالة الرسالة تلقائياً
        if (duration > 0) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 150);
                }
            }, duration);
        }
    }

    // تشغيل صوت الإشعار
    playNotificationSound(type) {
        if (!this.config.soundEnabled) return;

        // يمكن إضافة ملفات صوتية
        try {
            const audio = new Audio(`assets/sounds/${type}.mp3`);
            audio.volume = 0.3;
            audio.play().catch(() => {}); // تجاهل الأخطاء
        } catch (error) {
            // تجاهل الأخطاء
        }
    }

    // إظهار مؤشر التحميل على الزر
    showLoadingButton(button) {
        if (!button) return;
        
        button.disabled = true;
        button.innerHTML = '<span class="loading-spinner"></span> جاري التحديث...';
    }

    // إخفاء مؤشر التحميل من الزر
    hideLoadingButton(button) {
        if (!button) return;
        
        button.disabled = false;
        // استعادة النص الأصلي (يمكن تحسينه)
        button.innerHTML = button.getAttribute('data-original-text') || 'تحديث';
    }

    // تحريك البطاقات عند التحميل
    animateCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                card.classList.add('fade-in');
            }, index * 100);
        });
    }

    // تحريك صفوف الطلبيات
    animateOrderRows() {
        const rows = document.querySelectorAll('.order-row');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(20px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }

    // تهيئة الرسوم المتحركة
    initAnimations() {
        // تأثير hover للبطاقات
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    // تهيئة التلميحات
    initTooltips() {
        // تهيئة Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // تأكيد الحذف المتقدم
    confirmDelete(element, itemName = 'العنصر') {
        const result = confirm(`هل أنت متأكد من حذف ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);
        return result;
    }

    // تحديث الإحصائيات في الوقت الفعلي المحسن
    async updateStats() {
        try {
            const response = await fetch('api/get_stats.php', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // تحديث الإحصائيات مع تأثيرات بصرية
                Object.keys(data.stats).forEach(key => {
                    const element = document.getElementById(`stat-${key}`);
                    if (element) {
                        const oldValue = element.textContent;
                        const newValue = data.stats[key];

                        if (oldValue !== newValue) {
                            this.animateValueChange(element, oldValue, newValue);
                        }
                    }
                });

                // تحديث آخر وقت تحديث
                const lastUpdateElement = document.getElementById('last-update-time');
                if (lastUpdateElement) {
                    lastUpdateElement.textContent = new Date().toLocaleTimeString('ar-EG');
                }
            }
        } catch (error) {
            console.error('Error updating stats:', error);
            this.logError('stats_update_failed', error);
        }
    }

    // تحريك تغيير القيم
    animateValueChange(element, oldValue, newValue) {
        element.style.transform = 'scale(1.1)';
        element.style.color = '#28a745';

        setTimeout(() => {
            element.textContent = newValue;
            element.style.transform = 'scale(1)';

            setTimeout(() => {
                element.style.color = '';
            }, 300);
        }, 150);
    }

    // تسجيل النشاط
    logActivity(action, data = {}) {
        const activity = {
            action,
            data,
            timestamp: new Date().toISOString(),
            url: window.location.pathname,
            userAgent: navigator.userAgent
        };

        // حفظ في localStorage للمراجعة
        const activities = JSON.parse(localStorage.getItem('widdx_activities') || '[]');
        activities.push(activity);

        // الاحتفاظ بآخر 100 نشاط فقط
        if (activities.length > 100) {
            activities.splice(0, activities.length - 100);
        }

        localStorage.setItem('widdx_activities', JSON.stringify(activities));

        // إرسال للخادم (اختياري)
        if (this.config.logToServer) {
            this.sendActivityToServer(activity);
        }
    }

    // إرسال النشاط للخادم
    async sendActivityToServer(activity) {
        try {
            await fetch('api/log_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(activity)
            });
        } catch (error) {
            // تجاهل أخطاء التسجيل
        }
    }

    // تسجيل الأخطاء
    logError(type, error) {
        const errorLog = {
            type,
            message: error.message || error,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        console.error('WIDDX Error:', errorLog);

        // حفظ في localStorage
        const errors = JSON.parse(localStorage.getItem('widdx_errors') || '[]');
        errors.push(errorLog);

        if (errors.length > 50) {
            errors.splice(0, errors.length - 50);
        }

        localStorage.setItem('widdx_errors', JSON.stringify(errors));
    }

    // معالجة تغيير الاتصال
    handleConnectionChange(isOnline) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            if (isOnline) {
                statusElement.className = 'badge bg-success';
                statusElement.textContent = 'متصل';
                this.showAlert('تم استعادة الاتصال بالإنترنت', 'success', 3000);
            } else {
                statusElement.className = 'badge bg-danger';
                statusElement.textContent = 'غير متصل';
                this.showAlert('انقطع الاتصال بالإنترنت', 'warning', 5000);
            }
        }
    }

    // معالجة تغيير حجم النافذة
    handleResize() {
        // إعادة حساب أحجام العناصر
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            // تحديث ارتفاعات البطاقات إذا لزم الأمر
        });

        // تحديث الجداول المتجاوبة
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach(table => {
            // تحديث عرض الجداول
        });
    }

    // البحث المباشر المحسن
    liveSearch(input, targetContainer, options = {}) {
        const searchTerm = input.value.toLowerCase().trim();
        const items = document.querySelectorAll(`${targetContainer} .searchable-item`);
        const minLength = options.minLength || 2;
        const highlightMatches = options.highlight !== false;

        // إذا كان النص أقل من الحد الأدنى، إظهار جميع العناصر
        if (searchTerm.length < minLength) {
            items.forEach(item => {
                item.style.display = 'block';
                item.classList.add('fade-in');
                if (highlightMatches) {
                    this.removeHighlight(item);
                }
            });
            return;
        }

        let visibleCount = 0;

        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            const isMatch = text.includes(searchTerm);

            if (isMatch) {
                item.style.display = 'block';
                item.classList.add('fade-in');
                visibleCount++;

                if (highlightMatches) {
                    this.highlightText(item, searchTerm);
                }
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');

                if (highlightMatches) {
                    this.removeHighlight(item);
                }
            }
        });

        // إظهار رسالة إذا لم توجد نتائج
        this.updateSearchResults(targetContainer, visibleCount, searchTerm);
    }

    // تمييز النص المطابق
    highlightText(element, searchTerm) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            if (regex.test(text)) {
                const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');
                const span = document.createElement('span');
                span.innerHTML = highlightedText;
                textNode.parentNode.replaceChild(span, textNode);
            }
        });
    }

    // إزالة التمييز
    removeHighlight(element) {
        const highlights = element.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            highlight.outerHTML = highlight.innerHTML;
        });
    }

    // تحديث نتائج البحث
    updateSearchResults(container, count, searchTerm) {
        let resultsElement = document.querySelector(`${container} .search-results`);

        if (!resultsElement) {
            resultsElement = document.createElement('div');
            resultsElement.className = 'search-results alert alert-info mt-3';
            document.querySelector(container).appendChild(resultsElement);
        }

        if (count === 0) {
            resultsElement.innerHTML = `
                <i class="fas fa-search me-2"></i>
                لم يتم العثور على نتائج للبحث عن "<strong>${searchTerm}</strong>"
            `;
            resultsElement.style.display = 'block';
        } else {
            resultsElement.style.display = 'none';
        }
    }

    // تصدير البيانات المحسن
    exportData(format = 'excel', type = 'orders', filters = {}) {
        showGlobalLoading('جاري تحضير البيانات للتصدير...');

        const params = new URLSearchParams({
            format,
            type,
            timestamp: Date.now(),
            ...filters
        });

        const url = `export.php?${params.toString()}`;

        // إنشاء رابط مخفي للتحميل
        const link = document.createElement('a');
        link.href = url;
        link.download = `${type}_${new Date().toISOString().split('T')[0]}.${format}`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            hideGlobalLoading();
            this.showAlert('تم بدء تحميل الملف', 'success', 3000);
        }, 1000);

        // تسجيل النشاط
        this.logActivity('data_exported', { format, type, filters });
    }

    // طباعة التقرير المحسنة
    printReport(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            this.showAlert('العنصر المطلوب طباعته غير موجود', 'error');
            return;
        }

        showGlobalLoading('جاري تحضير التقرير للطباعة...');

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        const printContent = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>تقرير WIDDX OMS - ${new Date().toLocaleDateString('ar-EG')}</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="assets/css/main.css" rel="stylesheet">
                    <style>
                        body {
                            direction: rtl;
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            background: white;
                        }
                        .no-print { display: none !important; }
                        .card { box-shadow: none; border: 1px solid #dee2e6; }
                        @media print {
                            .btn, .no-print { display: none !important; }
                            .card { break-inside: avoid; }
                        }
                        .print-header {
                            text-align: center;
                            margin-bottom: 2rem;
                            border-bottom: 2px solid #dee2e6;
                            padding-bottom: 1rem;
                        }
                        .print-footer {
                            text-align: center;
                            margin-top: 2rem;
                            border-top: 1px solid #dee2e6;
                            padding-top: 1rem;
                            font-size: 0.9rem;
                            color: #6c757d;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="print-header">
                            <h2><i class="fas fa-box me-2"></i>WIDDX OMS</h2>
                            <p class="mb-0">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
                        </div>
                        ${element.innerHTML}
                        <div class="print-footer">
                            <p class="mb-0">تم إنشاء هذا التقرير بواسطة نظام WIDDX OMS</p>
                        </div>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            };
                        };
                    </script>
                </body>
            </html>
        `;

        printWindow.document.write(printContent);
        printWindow.document.close();

        setTimeout(() => {
            hideGlobalLoading();
        }, 500);

        // تسجيل النشاط
        this.logActivity('report_printed', { elementId });
    }

    // تهيئة التحميل التدريجي
    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const lazyImages = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                });
            });

            lazyImages.forEach(img => imageObserver.observe(img));
        }
    }

    // إعداد التحقق من النماذج
    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    // التحقق من صحة النموذج
    validateForm(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        return isValid;
    }

    // إظهار خطأ في الحقل
    showFieldError(field, message) {
        field.classList.add('is-invalid');

        let errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            field.parentNode.appendChild(errorElement);
        }

        errorElement.textContent = message;
    }

    // إزالة خطأ الحقل
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // إظهار نافذة تأكيد محسنة
    showConfirmDialog(title, message, confirmText = 'تأكيد', cancelText = 'إلغاء') {
        return new Promise((resolve) => {
            const modalId = 'confirm-modal-' + Date.now();
            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                                <button type="button" class="btn btn-danger confirm-btn">${confirmText}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById(modalId));

            document.querySelector(`#${modalId} .confirm-btn`).addEventListener('click', () => {
                modal.hide();
                resolve(true);
            });

            modal._element.addEventListener('hidden.bs.modal', () => {
                document.getElementById(modalId).remove();
                resolve(false);
            });

            modal.show();
        });
    }
}

// إنشاء مثيل من النظام
const widdxSystem = new WIDDXSystem();

// وظائف عامة للاستخدام في الصفحات
function loadCustomerOrders(customerId, forceReload = false) {
    return widdxSystem.loadCustomerOrders(customerId, forceReload);
}

function updateOrderStatus(orderId, status, element = null) {
    return widdxSystem.updateOrderStatus(orderId, status, element);
}

function confirmDelete(element, itemName) {
    return widdxSystem.confirmDelete(element, itemName);
}

function liveSearch(input, targetContainer, options = {}) {
    return widdxSystem.liveSearch(input, targetContainer, options);
}

function exportData(format = 'excel', type = 'orders', filters = {}) {
    return widdxSystem.exportData(format, type, filters);
}

function printReport(elementId) {
    return widdxSystem.printReport(elementId);
}

function showAlert(message, type = 'info', duration = 5000, actions = []) {
    return widdxSystem.showAlert(message, type, duration, actions);
}

// تحديث الإحصائيات بناءً على إعدادات المستخدم
function startStatsUpdater() {
    const interval = userSettings?.refreshInterval || 60;
    if (interval > 0) {
        setInterval(() => {
            widdxSystem.updateStats();
        }, interval * 1000);
    }
}

// بدء تحديث الإحصائيات
startStatsUpdater();

// تصدير النظام للاستخدام العام
window.WIDDX = {
    system: widdxSystem,
    loadCustomerOrders,
    updateOrderStatus,
    confirmDelete,
    liveSearch,
    exportData,
    printReport,
    showAlert
};

// تسجيل تحميل النظام
console.log('WIDDX OMS System loaded successfully');

// إضافة معلومات النظام للـ console
console.log('%c🚀 WIDDX OMS v2.0.0', 'color: #667eea; font-size: 16px; font-weight: bold;');
console.log('%cSystem initialized with advanced features', 'color: #28a745; font-size: 12px;');

// معلومات للمطورين
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('%c🔧 Development Mode', 'color: #ffc107; font-size: 14px; font-weight: bold;');
    console.log('Available global functions:', Object.keys(window.WIDDX));

    // إضافة أدوات تطوير
    window.WIDDX.dev = {
        clearCache: () => {
            widdxSystem.cache.clear();
            localStorage.removeItem('widdx_activities');
            localStorage.removeItem('widdx_errors');
            console.log('Cache and logs cleared');
        },
        getActivities: () => JSON.parse(localStorage.getItem('widdx_activities') || '[]'),
        getErrors: () => JSON.parse(localStorage.getItem('widdx_errors') || '[]'),
        getCache: () => Array.from(widdxSystem.cache.entries()),
        testNotification: (type = 'info') => {
            showAlert(`Test notification - ${type}`, type, 3000);
        }
    };
}
