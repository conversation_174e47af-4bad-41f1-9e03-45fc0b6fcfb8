# WIDDX OMS - نظام إدارة الطلبيات

## نظرة عامة
نظام إدارة الطلبيات WIDDX OMS هو نظام شامل لإدارة العملاء والطلبيات مع واجهة سهلة الاستخدام باللغة العربية.

## الإصدار الجديد 2.0.0
تم تحديث النظام بالكامل ليتبع نمط البرمجة الكائنية (OOP) مع تحسينات كبيرة في الأداء والأمان.

## الميزات الجديدة

### 🏗️ البنية المحسنة
- **نمط OOP**: تم إعادة كتابة النظام بالكامل باستخدام البرمجة الكائنية
- **فصل الاهتمامات**: فصل CSS و JavaScript إلى ملفات منفصلة
- **نظام Autoload**: تحميل تلقائي للكلاسات
- **إدارة الأخطاء**: نظام متقدم لتسجيل ومعالجة الأخطاء

### 📁 هيكل المجلدات الجديد
```
├── assets/
│   ├── css/
│   │   └── main.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── classes/
│   ├── Database.php
│   ├── Customer.php
│   └── Order.php
├── config/
│   ├── config.php
│   ├── database.php
│   └── autoload.php
├── includes/
│   ├── header.php
│   └── footer.php
├── api/
│   └── quick_stats.php
├── logs/
└── uploads/
```

### 🎨 تحسينات الواجهة
- **تصميم موحد**: استخدام header و footer مشتركين
- **تأثيرات بصرية**: رسوم متحركة وتأثيرات تفاعلية
- **استجابة محسنة**: تحسينات للشاشات المختلفة
- **إشعارات ذكية**: نظام إشعارات في الوقت الفعلي

### 🔒 تحسينات الأمان
- **Prepared Statements**: حماية من SQL Injection
- **تنظيف المدخلات**: تنظيف جميع المدخلات من المستخدم
- **تسجيل الأخطاء**: تسجيل مفصل للأخطاء والأنشطة
- **إدارة الجلسات**: نظام محسن لإدارة الجلسات

### ⚡ تحسينات الأداء
- **Singleton Pattern**: استخدام نمط Singleton لقاعدة البيانات
- **تحميل تدريجي**: تحميل المحتوى حسب الحاجة
- **ذاكرة التخزين المؤقت**: تحسينات في استخدام الذاكرة

## متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مساحة تخزين: 50 ميجابايت على الأقل

## التثبيت

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd widdx-oms
```

### 2. إعداد قاعدة البيانات
1. قم بإنشاء قاعدة بيانات جديدة
2. قم بتحديث إعدادات قاعدة البيانات في `config/config.php`
3. قم بتشغيل `install.php` لإنشاء الجداول

### 3. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 logs/
chmod 644 config/config.php
```

### 4. اختبار النظام
- قم بزيارة `system_check.php` للتأكد من سلامة النظام
- قم بزيارة `index.php` لبدء الاستخدام

## الاستخدام

### إدارة العملاء
- **إضافة عميل**: `add_customer.php`
- **إدارة العملاء**: `manage_customers.php`
- **البحث والفلترة**: إمكانيات بحث متقدمة

### إدارة الطلبيات
- **إضافة طلبية**: `add_order.php`
- **عرض الطلبيات**: `view_orders.php`
- **تفاصيل الطلبية**: `view_order.php`
- **تحديث الحالة**: تحديث فوري لحالة الطلبيات

### لوحة التحكم
- **إحصائيات فورية**: عرض الإحصائيات في الوقت الفعلي
- **العملاء النشطين**: عرض العملاء الذين لديهم طلبيات نشطة
- **إشعارات**: تنبيهات للطلبيات المعلقة والمتأخرة

## API

### الإحصائيات السريعة
```javascript
GET /api/quick_stats.php
```
يعيد إحصائيات سريعة وإشعارات النظام.

## الكلاسات الرئيسية

### Database
```php
$db = Database::getInstance();
$customers = $db->select("SELECT * FROM customers");
```

### Customer
```php
$customerManager = new Customer();
$result = $customerManager->create($customerData);
$customers = $customerManager->getAllWithStats();
```

### Order
```php
$orderManager = new Order();
$result = $orderManager->create($orderData);
$orders = $orderManager->getAllWithFilters($filters);
```

## التخصيص

### إضافة CSS مخصص
أضف الأنماط المخصصة في `assets/css/main.css`

### إضافة JavaScript مخصص
أضف الوظائف المخصصة في `assets/js/main.js`

### إضافة كلاسات جديدة
1. أنشئ الكلاس في مجلد `classes/`
2. سيتم تحميله تلقائياً بواسطة نظام Autoload

## استكشاف الأخطاء

### تفعيل وضع التطوير
```php
// في config/config.php
define('DEBUG_MODE', true);
```

### عرض السجلات
```bash
tail -f logs/error.log
tail -f logs/system.log
```

### فحص النظام
قم بزيارة `system_check.php` لفحص شامل للنظام

## الأمان

### أفضل الممارسات
- قم بتحديث كلمات المرور بانتظام
- راقب ملفات السجلات
- قم بعمل نسخ احتياطية دورية
- حدث النظام بانتظام

### حماية الملفات الحساسة
```apache
# .htaccess في مجلد logs/
Order Deny,Allow
Deny from all
```

## النسخ الاحتياطي

### قاعدة البيانات
```bash
mysqldump -u username -p widdx_oms > backup.sql
```

### الملفات
```bash
tar -czf widdx_backup.tar.gz /path/to/widdx-oms/
```

## الدعم والمساهمة

### الإبلاغ عن الأخطاء
يرجى الإبلاغ عن أي أخطاء أو مشاكل عبر نظام إدارة المشاكل.

### المساهمة
نرحب بالمساهمات! يرجى اتباع معايير الكود المحددة.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## تاريخ الإصدارات

### الإصدار 2.0.0 (الحالي)
- إعادة كتابة كاملة بنمط OOP
- تحسينات الأمان والأداء
- واجهة مستخدم محسنة
- نظام إدارة أخطاء متقدم

### الإصدار 1.0.0
- النسخة الأولى الأساسية
- وظائف إدارة العملاء والطلبيات الأساسية

---

**تم التطوير بواسطة WIDDX Team**
