<?php
/**
 * ملف الإعدادات الرئيسي لنظام WIDDX OMS
 * يحتوي على جميع الإعدادات والثوابت المطلوبة للنظام
 */

// منع الوصول المباشر للملف
if (!defined('WIDDX_OMS')) {
    define('WIDDX_OMS', true);
}

// إعدادات قاعدة البيانات
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');
if (!defined('DB_NAME')) define('DB_NAME', 'widdx_oms');

// إعدادات النظام العامة
define('SYSTEM_NAME', 'WIDDX OMS');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_AUTHOR', 'WIDDX Team');
define('SYSTEM_EMAIL', '<EMAIL>');

// إعدادات المسارات
define('BASE_PATH', dirname(__DIR__));
define('ASSETS_PATH', BASE_PATH . '/assets');
define('UPLOADS_PATH', BASE_PATH . '/uploads');
define('LOGS_PATH', BASE_PATH . '/logs');
define('CLASSES_PATH', BASE_PATH . '/classes');
define('INCLUDES_PATH', BASE_PATH . '/includes');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 6);
define('CSRF_TOKEN_EXPIRE', 1800); // 30 دقيقة

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx']);

// إعدادات التطبيق
define('DEFAULT_TIMEZONE', 'Africa/Cairo');
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_CURRENCY', 'EGP');
define('ITEMS_PER_PAGE', 20);

// إعدادات البريد الإلكتروني (إذا كان مطلوباً)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('SHOW_ERRORS', DEBUG_MODE);

// إعدادات الجلسة (فقط إذا لم تبدأ الجلسة بعد)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS
    ini_set('session.use_strict_mode', 1);
}

// إعدادات المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات عرض الأخطاء
if (SHOW_ERRORS) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

/**
 * كلاس الإعدادات
 */
class Config {
    private static $settings = [];
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    public static function loadFromDatabase() {
        try {
            // التحقق من وجود الثوابت المطلوبة
            if (!defined('DB_HOST') || !defined('DB_NAME')) {
                return;
            }

            // محاولة الاتصال بقاعدة البيانات
            $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4';
            $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);

            // التحقق من وجود جدول الإعدادات
            $tableExists = $pdo->query("SHOW TABLES LIKE 'settings'")->rowCount() > 0;

            if ($tableExists) {
                $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
                $settings = $stmt->fetchAll();

                foreach ($settings as $setting) {
                    self::$settings[$setting['setting_key']] = $setting['setting_value'];
                }
            }
        } catch (Exception $e) {
            // في حالة فشل تحميل الإعدادات، استخدم الإعدادات الافتراضية
            if (LOG_ERRORS) {
                error_log("Failed to load settings from database: " . $e->getMessage());
            }
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public static function get($key, $default = null) {
        return self::$settings[$key] ?? $default;
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public static function set($key, $value) {
        self::$settings[$key] = $value;
    }
    
    /**
     * حفظ إعداد في قاعدة البيانات
     */
    public static function save($key, $value) {
        try {
            require_once CLASSES_PATH . '/Database.php';
            $db = Database::getInstance();
            
            $query = "INSERT INTO settings (setting_key, setting_value) 
                     VALUES (:key, :value) 
                     ON DUPLICATE KEY UPDATE setting_value = :value";
            
            $db->query($query);
            $db->bind(':key', $key);
            $db->bind(':value', $value);
            $db->execute();
            
            self::$settings[$key] = $value;
            return true;
        } catch (Exception $e) {
            error_log("Failed to save setting: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll() {
        return self::$settings;
    }
}

/**
 * كلاس المساعدات العامة
 */
class Helper {
    /**
     * تنظيف النص من الأكواد الضارة
     */
    public static function sanitize($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitize'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    public static function validatePhone($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        return strlen($phone) >= 10 && strlen($phone) <= 15;
    }
    
    /**
     * تنسيق التاريخ
     */
    public static function formatDate($date, $format = 'Y-m-d H:i:s') {
        if (empty($date)) return '';
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $date;
        }
    }
    
    /**
     * تنسيق المبلغ المالي
     */
    public static function formatMoney($amount, $currency = DEFAULT_CURRENCY) {
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * إنشاء رمز عشوائي
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * إنشاء رابط آمن
     */
    public static function createSecureUrl($page, $params = []) {
        $url = $page;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        return $url;
    }
    
    /**
     * تحويل الحجم إلى تنسيق قابل للقراءة
     */
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * إنشاء مجلد إذا لم يكن موجوداً
     */
    public static function createDirectoryIfNotExists($path) {
        if (!is_dir($path)) {
            return mkdir($path, 0755, true);
        }
        return true;
    }
    
    /**
     * تسجيل الأخطاء
     */
    public static function logError($message, $file = 'error.log') {
        if (!LOG_ERRORS) return;
        
        $logDir = LOGS_PATH;
        self::createDirectoryIfNotExists($logDir);
        
        $logFile = $logDir . '/' . $file;
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * إعادة توجيه آمنة
     */
    public static function redirect($url, $statusCode = 302) {
        header("Location: {$url}", true, $statusCode);
        exit;
    }
    
    /**
     * التحقق من طلب AJAX
     */
    public static function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * إرسال استجابة JSON
     */
    public static function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * كلاس إدارة الجلسات
 */
class Session {
    /**
     * بدء الجلسة
     */
    public static function start() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * تعيين قيمة في الجلسة
     */
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * الحصول على قيمة من الجلسة
     */
    public static function get($key, $default = null) {
        self::start();
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * حذف قيمة من الجلسة
     */
    public static function delete($key) {
        self::start();
        unset($_SESSION[$key]);
    }
    
    /**
     * تدمير الجلسة
     */
    public static function destroy() {
        self::start();
        session_destroy();
    }
    
    /**
     * التحقق من وجود قيمة في الجلسة
     */
    public static function has($key) {
        self::start();
        return isset($_SESSION[$key]);
    }
}

// تحميل الإعدادات من قاعدة البيانات
Config::loadFromDatabase();

// بدء الجلسة (فقط إذا لم تبدأ بعد)
if (session_status() === PHP_SESSION_NONE) {
    Session::start();
}
